//
//  AccessibilityHelper.swift
//  FitBreak
//
//  Created by <PERSON> on 16/09/2025.
//

import UIKit
import SwiftUI

/// Helper class for managing accessibility features throughout the FitBreak app
class AccessibilityHelper {
    
    /// Posts an accessibility announcement with optional delay
    /// - Parameters:
    ///   - message: The message to announce
    ///   - delay: Optional delay before posting the announcement
    static func announce(_ message: String, delay: TimeInterval = 0) {
        if delay > 0 {
            DispatchQueue.main.asyncAfter(deadline: .now() + delay) {
                UIAccessibility.post(notification: .announcement, argument: message)
            }
        } else {
            UIAccessibility.post(notification: .announcement, argument: message)
        }
    }
    
    /// Posts a screen change notification for VoiceOver users
    /// - Parameters:
    ///   - message: The message to announce with the screen change
    ///   - delay: Optional delay before posting the notification
    static func announceScreenChange(_ message: String, delay: TimeInterval = 0) {
        if delay > 0 {
            DispatchQueue.main.asyncAfter(deadline: .now() + delay) {
                UIAccessibility.post(notification: .screenChanged, argument: message)
            }
        } else {
            UIAccessibility.post(notification: .screenChanged, argument: message)
        }
    }
    
    /// Checks if VoiceOver is currently running
    static var isVoiceOverRunning: Bool {
        return UIAccessibility.isVoiceOverRunning
    }
    
    /// Checks if the user prefers reduced motion
    static var prefersReducedMotion: Bool {
        return UIAccessibility.isReduceMotionEnabled
    }
    
    /// Formats time for accessibility announcements
    /// - Parameter timeInterval: Time in seconds
    /// - Returns: Formatted string suitable for VoiceOver
    static func formatTimeForAccessibility(_ timeInterval: TimeInterval) -> String {
        let minutes = Int(timeInterval) / 60
        let seconds = Int(timeInterval) % 60
        
        if minutes > 0 && seconds > 0 {
            return "\(minutes) minute\(minutes == 1 ? "" : "s") and \(seconds) second\(seconds == 1 ? "" : "s")"
        } else if minutes > 0 {
            return "\(minutes) minute\(minutes == 1 ? "" : "s")"
        } else {
            return "\(seconds) second\(seconds == 1 ? "" : "s")"
        }
    }
    
    /// Creates an accessibility label for exercise instructions
    /// - Parameters:
    ///   - stepNumber: The step number
    ///   - instruction: The instruction text
    /// - Returns: Formatted accessibility label
    static func exerciseStepLabel(stepNumber: String, instruction: String) -> String {
        return "Step \(stepNumber): \(instruction)"
    }
    
    /// Creates an accessibility hint for media controls
    /// - Parameters:
    ///   - action: The action (play, pause, stop)
    ///   - mediaType: The type of media (audio, video)
    /// - Returns: Formatted accessibility hint
    static func mediaControlHint(action: String, mediaType: String) -> String {
        return "Double tap to \(action) \(mediaType)"
    }
    
    /// Announces timer state changes for accessibility
    /// - Parameters:
    ///   - state: The current timer state
    ///   - timeRemaining: Time remaining in the session
    ///   - sessionType: Type of session (work, short break, long break)
    static func announceTimerStateChange(state: TimerState, timeRemaining: TimeInterval, sessionType: SessionType) {
        let timeString = formatTimeForAccessibility(timeRemaining)
        let sessionName = sessionType.displayName.lowercased()
        
        switch state {
        case .running:
            announce("\(sessionName.capitalized) started. \(timeString) remaining.")
        case .paused:
            announce("\(sessionName.capitalized) paused. \(timeString) remaining.")
        case .completed:
            announce("\(sessionName.capitalized) completed.")
        case .idle:
            announce("\(sessionName.capitalized) ready to start.")
        }
    }
    
    /// Announces exercise progress for accessibility
    /// - Parameters:
    ///   - exerciseName: Name of the exercise
    ///   - timeRemaining: Time remaining in the exercise
    static func announceExerciseProgress(exerciseName: String, timeRemaining: TimeInterval) {
        let timeString = formatTimeForAccessibility(timeRemaining)
        announce("\(exerciseName) in progress. \(timeString) remaining.")
    }
    
    /// Creates accessibility labels for motivational messages
    /// - Parameter message: The motivational message
    /// - Returns: Formatted accessibility label
    static func motivationalMessageLabel(_ message: String) -> String {
        // Remove emojis for cleaner VoiceOver reading
        let cleanMessage = message.replacingOccurrences(of: "💪", with: "")
                                 .replacingOccurrences(of: "⚡", with: "")
                                 .replacingOccurrences(of: "🌟", with: "")
                                 .replacingOccurrences(of: "🔥", with: "")
                                 .replacingOccurrences(of: "🎯", with: "")
                                 .replacingOccurrences(of: "❤️", with: "")
                                 .replacingOccurrences(of: "🏃‍♀️", with: "")
                                 .replacingOccurrences(of: "🚀", with: "")
                                 .replacingOccurrences(of: "✨", with: "")
                                 .replacingOccurrences(of: "🌈", with: "")
                                 .trimmingCharacters(in: .whitespacesAndNewlines)
        
        return "Motivation: \(cleanMessage)"
    }
}

// MARK: - SwiftUI View Modifiers for Accessibility

extension View {
    /// Adds consistent accessibility support for exercise instruction steps
    /// - Parameters:
    ///   - stepNumber: The step number
    ///   - instruction: The instruction text
    /// - Returns: Modified view with accessibility support
    func exerciseStepAccessibility(stepNumber: String, instruction: String) -> some View {
        self
            .accessibilityElement(children: .combine)
            .accessibilityLabel(AccessibilityHelper.exerciseStepLabel(stepNumber: stepNumber, instruction: instruction))
    }
    
    /// Adds consistent accessibility support for timer displays
    /// - Parameters:
    ///   - timeRemaining: Current time remaining
    ///   - sessionType: Type of session
    /// - Returns: Modified view with accessibility support
    func timerDisplayAccessibility(timeRemaining: TimeInterval, sessionType: SessionType) -> some View {
        self
            .accessibilityElement(children: .combine)
            .accessibilityLabel("Timer display. \(AccessibilityHelper.formatTimeForAccessibility(timeRemaining)) remaining for \(sessionType.displayName)")
            .accessibilityAddTraits(.updatesFrequently)
    }
    
    /// Adds consistent accessibility support for media controls
    /// - Parameters:
    ///   - action: The action performed by the control
    ///   - mediaType: Type of media being controlled
    /// - Returns: Modified view with accessibility support
    func mediaControlAccessibility(action: String, mediaType: String) -> some View {
        self
            .accessibilityHint(AccessibilityHelper.mediaControlHint(action: action, mediaType: mediaType))
    }
    
    /// Adds accessibility support for motivational messages
    /// - Parameter message: The motivational message
    /// - Returns: Modified view with accessibility support
    func motivationalMessageAccessibility(_ message: String) -> some View {
        self
            .accessibilityLabel(AccessibilityHelper.motivationalMessageLabel(message))
            .accessibilityAddTraits(.updatesFrequently)
    }
    
    /// Adds Dynamic Type support with custom scaling limits
    /// - Parameters:
    ///   - minScale: Minimum scale factor
    ///   - maxScale: Maximum scale factor
    /// - Returns: Modified view with Dynamic Type support
    func dynamicTypeSize(minScale: Double = 0.8, maxScale: Double = 1.3) -> some View {
        self
            .dynamicTypeSize(...DynamicTypeSize.accessibility3)
    }
    
    /// Adds reduced motion support for animations
    /// - Parameter animation: The animation to conditionally apply
    /// - Returns: Modified view with reduced motion support
    func reducedMotionAnimation<V: Equatable>(_ animation: Animation?, value: V) -> some View {
        self
            .animation(AccessibilityHelper.prefersReducedMotion ? nil : animation, value: value)
    }
}
