//
//  LaunchScreenView.swift
//  FitBreak
//
//  Created by <PERSON> on 16/09/2025.
//

import SwiftUI

struct LaunchScreenView: View {
    @State private var isAnimating = false
    @State private var opacity = 0.0
    
    var body: some View {
        ZStack {
            // Background gradient
            LinearGradient(
                gradient: Gradient(colors: [
                    Color.purple.opacity(0.8),
                    Color.purple.opacity(0.6),
                    Color.blue.opacity(0.4)
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()
            
            VStack(spacing: 30) {
                // App icon/logo
                ZStack {
                    Circle()
                        .stroke(Color.white.opacity(0.3), lineWidth: 4)
                        .frame(width: 120, height: 120)
                    
                    Circle()
                        .trim(from: 0, to: isAnimating ? 1 : 0)
                        .stroke(
                            Color.white,
                            style: StrokeStyle(lineWidth: 4, lineCap: .round)
                        )
                        .frame(width: 120, height: 120)
                        .rotationEffect(.degrees(-90))
                        .animation(.easeInOut(duration: 2).repeatFore<PERSON>(autoreverses: false), value: isAnimating)
                    
                    Image(systemName: "timer")
                        .font(.system(size: 40, weight: .light))
                        .foregroundColor(.white)
                }
                
                // App name
                VStack(spacing: 8) {
                    Text("FitBreak")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                    
                    Text("Focus • Break • Achieve")
                        .font(.headline)
                        .foregroundColor(.white.opacity(0.8))
                }
                .opacity(opacity)
            }
        }
        .onAppear {
            withAnimation(.easeInOut(duration: 1)) {
                opacity = 1.0
            }
            
            withAnimation(.easeInOut(duration: 2).delay(0.5)) {
                isAnimating = true
            }
        }
    }
}

#Preview {
    LaunchScreenView()
}
