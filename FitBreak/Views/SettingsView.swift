//
//  SettingsView.swift
//  FitBreak
//
//  Created by <PERSON> on 16/09/2025.
//

import SwiftUI

struct SettingsView: View {
    @StateObject private var settingsManager = SettingsManager()
    @State private var showingResetAlert = false
    @State private var showingExportSheet = false
    
    var body: some View {
        NavigationView {
            Form {
                timerDurationsSection
                behaviorSection
                notificationsSection
                soundsSection
                dataSection
                aboutSection
            }
            .navigationTitle("Settings")
            .alert("Reset Settings", isPresented: $showingResetAlert) {
                Button("Cancel", role: .cancel) { }
                Button("Reset", role: .destructive) {
                    settingsManager.resetToDefaults()
                }
            } message: {
                Text("This will reset all settings to their default values. This action cannot be undone.")
            }
            .sheet(isPresented: $showingExportSheet) {
                ExportDataView()
            }
        }
    }
    
    private var timerDurationsSection: some View {
        Section("Timer Durations") {
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("Work Session")
                    Spacer()
                    Text(settingsManager.workSessionDisplayText)
                        .foregroundColor(.secondary)
                }
                
                Slider(
                    value: Binding(
                        get: { Double(settingsManager.workSessionDuration) },
                        set: { settingsManager.workSessionDuration = Int($0) }
                    ),
                    in: Double(settingsManager.workSessionRange.lowerBound)...Double(settingsManager.workSessionRange.upperBound),
                    step: 1
                )
                .accentColor(.blue)
            }
            
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("Short Break")
                    Spacer()
                    Text(settingsManager.shortBreakDisplayText)
                        .foregroundColor(.secondary)
                }
                
                Slider(
                    value: Binding(
                        get: { Double(settingsManager.shortBreakDuration) },
                        set: { settingsManager.shortBreakDuration = Int($0) }
                    ),
                    in: Double(settingsManager.shortBreakRange.lowerBound)...Double(settingsManager.shortBreakRange.upperBound),
                    step: 1
                )
                .accentColor(.green)
            }
            
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("Long Break")
                    Spacer()
                    Text(settingsManager.longBreakDisplayText)
                        .foregroundColor(.secondary)
                }
                
                Slider(
                    value: Binding(
                        get: { Double(settingsManager.longBreakDuration) },
                        set: { settingsManager.longBreakDuration = Int($0) }
                    ),
                    in: Double(settingsManager.longBreakRange.lowerBound)...Double(settingsManager.longBreakRange.upperBound),
                    step: 1
                )
                .accentColor(.purple)
            }
            
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("Pomodoros until Long Break")
                    Spacer()
                    Text(settingsManager.pomodoroCountDisplayText)
                        .foregroundColor(.secondary)
                }
                
                Slider(
                    value: Binding(
                        get: { Double(settingsManager.pomodorosUntilLongBreak) },
                        set: { settingsManager.pomodorosUntilLongBreak = Int($0) }
                    ),
                    in: Double(settingsManager.pomodoroCountRange.lowerBound)...Double(settingsManager.pomodoroCountRange.upperBound),
                    step: 1
                )
                .accentColor(.orange)
            }
        }
    }
    
    private var behaviorSection: some View {
        Section("Behavior") {
            Toggle("Auto-start work sessions", isOn: $settingsManager.autoStartSessions)
            
            Toggle("Auto-start breaks", isOn: $settingsManager.autoStartBreaks)
            
            Toggle("Haptic feedback", isOn: $settingsManager.hapticFeedbackEnabled)
        }
    }
    
    private var notificationsSection: some View {
        Section("Notifications") {
            Toggle("Enable notifications", isOn: $settingsManager.notificationsEnabled)
            
            if !settingsManager.notificationsEnabled {
                Text("Notifications are disabled. You won't receive alerts when sessions complete.")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
    }
    
    private var soundsSection: some View {
        Section("Sounds") {
            Picker("Completion sound", selection: $settingsManager.selectedSoundName) {
                ForEach(settingsManager.availableSounds.sorted(by: { $0.key < $1.key }), id: \.key) { key, value in
                    Text(value).tag(key)
                }
            }
            
            if settingsManager.selectedSoundName == "silent" {
                Text("Silent mode selected. No sound will play when sessions complete.")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
    }
    
    private var dataSection: some View {
        Section("Data") {
            Button("Export data") {
                showingExportSheet = true
            }
            
            Button("Reset all settings") {
                showingResetAlert = true
            }
            .foregroundColor(.red)
        }
    }
    
    private var aboutSection: some View {
        Section("About") {
            HStack {
                Text("Version")
                Spacer()
                Text("1.0.0")
                    .foregroundColor(.secondary)
            }
            
            HStack {
                Text("Build")
                Spacer()
                Text("1")
                    .foregroundColor(.secondary)
            }
            
            Link("Privacy Policy", destination: URL(string: "https://example.com/privacy")!)
            
            Link("Terms of Service", destination: URL(string: "https://example.com/terms")!)
        }
    }
}

struct ExportDataView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var settingsManager = SettingsManager()
    @StateObject private var statisticsManager = StatisticsManager()
    @State private var exportText = ""
    @State private var showingShareSheet = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Text("Export Your Data")
                    .font(.title2)
                    .fontWeight(.semibold)
                
                Text("Your settings and statistics will be exported as text that you can save or share.")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)
                
                ScrollView {
                    Text(exportText)
                        .font(.system(.caption, design: .monospaced))
                        .padding()
                        .background(Color(.systemGray6))
                        .cornerRadius(8)
                }
                
                Button("Share Data") {
                    showingShareSheet = true
                }
                .buttonStyle(.borderedProminent)
                .disabled(exportText.isEmpty)
            }
            .padding()
            .navigationTitle("Export Data")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
            .onAppear {
                generateExportText()
            }
            .sheet(isPresented: $showingShareSheet) {
                ShareSheet(items: [exportText])
            }
        }
    }
    
    private func generateExportText() {
        let settings = settingsManager.exportSettings()
        
        var text = "FitBreak Data Export\n"
        text += "Generated: \(Date().formatted())\n\n"
        
        text += "SETTINGS\n"
        text += "========\n"
        text += "Work Session Duration: \(settings["workSessionDuration"] ?? 0) minutes\n"
        text += "Short Break Duration: \(settings["shortBreakDuration"] ?? 0) minutes\n"
        text += "Long Break Duration: \(settings["longBreakDuration"] ?? 0) minutes\n"
        text += "Pomodoros until Long Break: \(settings["pomodorosUntilLongBreak"] ?? 0)\n"
        text += "Auto-start Sessions: \(settings["autoStartSessions"] ?? false)\n"
        text += "Auto-start Breaks: \(settings["autoStartBreaks"] ?? false)\n"
        text += "Notifications Enabled: \(settings["notificationsEnabled"] ?? false)\n"
        text += "Selected Sound: \(settings["selectedSoundName"] ?? "default")\n"
        text += "Haptic Feedback: \(settings["hapticFeedbackEnabled"] ?? false)\n\n"
        
        text += "STATISTICS\n"
        text += "==========\n"
        text += "Current Streak: \(statisticsManager.currentStreak) days\n"
        text += "Best Streak: \(statisticsManager.bestStreak) days\n"
        text += "Total Focus Time: \(statisticsManager.totalFocusTimeFormatted)\n"
        text += "Completed Sessions: \(statisticsManager.completedSessions)\n"
        text += "Average Session Time: \(statisticsManager.averageSessionTimeFormatted)\n"
        
        exportText = text
    }
}

struct ShareSheet: UIViewControllerRepresentable {
    let items: [Any]
    
    func makeUIViewController(context: Context) -> UIActivityViewController {
        UIActivityViewController(activityItems: items, applicationActivities: nil)
    }
    
    func updateUIViewController(_ uiViewController: UIActivityViewController, context: Context) {}
}

#Preview {
    SettingsView()
}
