//
//  SettingsManager.swift
//  FitBreak
//
//  Created by <PERSON> on 16/09/2025.
//

import Foundation
import Combine

@MainActor
class SettingsManager: ObservableObject {
    @Published var workSessionDuration: Int = 25 {
        didSet { saveSettings() }
    }
    
    @Published var shortBreakDuration: Int = 5 {
        didSet { saveSettings() }
    }
    
    @Published var longBreakDuration: Int = 15 {
        didSet { saveSettings() }
    }
    
    @Published var pomodorosUntilLongBreak: Int = 4 {
        didSet { saveSettings() }
    }
    
    @Published var autoStartSessions: Bool = false {
        didSet { saveSettings() }
    }
    
    @Published var autoStartBreaks: Bool = false {
        didSet { saveSettings() }
    }
    
    @Published var notificationsEnabled: Bool = true {
        didSet { saveSettings() }
    }
    
    @Published var selectedSoundName: String = "default" {
        didSet { saveSettings() }
    }
    
    @Published var hapticFeedbackEnabled: Bool = true {
        didSet { saveSettings() }
    }

    @Published var fitnessFeatureEnabled: Bool = true {
        didSet { saveSettings() }
    }
    
    private let persistenceController = PersistenceController.shared
    private var userSettings: UserSettings
    
    // Available sound options
    let availableSounds = [
        "default": "Default",
        "bell": "Bell",
        "chime": "Chime",
        "ding": "Ding",
        "silent": "Silent"
    ]
    
    // Duration ranges
    let workSessionRange = 1...60
    let shortBreakRange = 3...15
    let longBreakRange = 15...45
    let pomodoroCountRange = 2...8
    
    init() {
        self.userSettings = persistenceController.getUserSettings()
        loadSettings()
    }
    
    private func loadSettings() {
        workSessionDuration = Int(userSettings.workSessionDuration) / 60
        shortBreakDuration = Int(userSettings.shortBreakDuration) / 60
        longBreakDuration = Int(userSettings.longBreakDuration) / 60
        pomodorosUntilLongBreak = Int(userSettings.pomodorosUntilLongBreak)
        autoStartSessions = userSettings.autoStartSessions
        autoStartBreaks = userSettings.autoStartBreaks
        notificationsEnabled = userSettings.notificationsEnabled
        selectedSoundName = userSettings.selectedSoundName ?? "default"
        fitnessFeatureEnabled = userSettings.fitnessFeatureEnabled
    }
    
    private func saveSettings() {
        userSettings.workSessionDuration = Int32(workSessionDuration * 60)
        userSettings.shortBreakDuration = Int32(shortBreakDuration * 60)
        userSettings.longBreakDuration = Int32(longBreakDuration * 60)
        userSettings.pomodorosUntilLongBreak = Int32(pomodorosUntilLongBreak)
        userSettings.autoStartSessions = autoStartSessions
        userSettings.autoStartBreaks = autoStartBreaks
        userSettings.notificationsEnabled = notificationsEnabled
        userSettings.selectedSoundName = selectedSoundName
        userSettings.fitnessFeatureEnabled = fitnessFeatureEnabled
        
        persistenceController.updateUserSettings(userSettings)
    }
    
    // MARK: - Validation
    
    func isValidWorkDuration(_ duration: Int) -> Bool {
        workSessionRange.contains(duration)
    }
    
    func isValidShortBreakDuration(_ duration: Int) -> Bool {
        shortBreakRange.contains(duration)
    }
    
    func isValidLongBreakDuration(_ duration: Int) -> Bool {
        longBreakRange.contains(duration)
    }
    
    func isValidPomodoroCount(_ count: Int) -> Bool {
        pomodoroCountRange.contains(count)
    }
    
    // MARK: - Reset to Defaults
    
    func resetToDefaults() {
        workSessionDuration = 25
        shortBreakDuration = 5
        longBreakDuration = 15
        pomodorosUntilLongBreak = 4
        autoStartSessions = false
        autoStartBreaks = false
        notificationsEnabled = true
        selectedSoundName = "default"
        hapticFeedbackEnabled = true
        fitnessFeatureEnabled = true
    }
    
    // MARK: - Export/Import Settings
    
    func exportSettings() -> [String: Any] {
        return [
            "workSessionDuration": workSessionDuration,
            "shortBreakDuration": shortBreakDuration,
            "longBreakDuration": longBreakDuration,
            "pomodorosUntilLongBreak": pomodorosUntilLongBreak,
            "autoStartSessions": autoStartSessions,
            "autoStartBreaks": autoStartBreaks,
            "notificationsEnabled": notificationsEnabled,
            "selectedSoundName": selectedSoundName,
            "hapticFeedbackEnabled": hapticFeedbackEnabled,
            "fitnessFeatureEnabled": fitnessFeatureEnabled
        ]
    }
    
    func importSettings(from data: [String: Any]) {
        if let workDuration = data["workSessionDuration"] as? Int,
           isValidWorkDuration(workDuration) {
            workSessionDuration = workDuration
        }
        
        if let shortBreak = data["shortBreakDuration"] as? Int,
           isValidShortBreakDuration(shortBreak) {
            shortBreakDuration = shortBreak
        }
        
        if let longBreak = data["longBreakDuration"] as? Int,
           isValidLongBreakDuration(longBreak) {
            longBreakDuration = longBreak
        }
        
        if let pomodoroCount = data["pomodorosUntilLongBreak"] as? Int,
           isValidPomodoroCount(pomodoroCount) {
            pomodorosUntilLongBreak = pomodoroCount
        }
        
        if let autoStart = data["autoStartSessions"] as? Bool {
            autoStartSessions = autoStart
        }
        
        if let autoBreak = data["autoStartBreaks"] as? Bool {
            autoStartBreaks = autoBreak
        }
        
        if let notifications = data["notificationsEnabled"] as? Bool {
            notificationsEnabled = notifications
        }
        
        if let soundName = data["selectedSoundName"] as? String,
           availableSounds.keys.contains(soundName) {
            selectedSoundName = soundName
        }
        
        if let haptic = data["hapticFeedbackEnabled"] as? Bool {
            hapticFeedbackEnabled = haptic
        }

        if let fitness = data["fitnessFeatureEnabled"] as? Bool {
            fitnessFeatureEnabled = fitness
        }
    }
    
    // MARK: - Formatted Display Values
    
    var workSessionDisplayText: String {
        "\(workSessionDuration) min"
    }
    
    var shortBreakDisplayText: String {
        "\(shortBreakDuration) min"
    }
    
    var longBreakDisplayText: String {
        "\(longBreakDuration) min"
    }
    
    var pomodoroCountDisplayText: String {
        "\(pomodorosUntilLongBreak) cycles"
    }
    
    var selectedSoundDisplayText: String {
        availableSounds[selectedSoundName] ?? "Default"
    }
}
