//
//  TimerView.swift
//  FitBreak
//
//  Created by <PERSON> on 16/09/2025.
//

import SwiftUI
import CoreData

struct TimerView: View {
    @StateObject private var timerManager = TimerManager()
    @StateObject private var settingsManager = SettingsManager()
    @State private var showingTaskSelector = false
    @State private var selectedTask: Task?
    @State private var showingExerciseInstruction = false
    @State private var showingExerciseWorkout = false
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // Background gradient
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color.purple.opacity(0.8),
                        Color.purple.opacity(0.6),
                        Color.blue.opacity(0.4)
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
                
                VStack(spacing: 30) {
                    // Header with task selection
                    headerView
                    
                    Spacer()
                    
                    // Main timer display
                    timerDisplayView(geometry: geometry)
                    
                    Spacer()
                    
                    // Timer controls
                    timerControlsView
                    
                    // Session type selector
                    sessionTypeSelectorView
                    
                    Spacer()
                }
                .padding()
            }
        }
        .preferredColorScheme(.dark)
        .sheet(isPresented: $showingTaskSelector) {
            TaskSelectorView(selectedTask: $selectedTask)
        }
        .fullScreenCover(isPresented: $showingExerciseInstruction) {
            ExerciseInstructionView {
                showingExerciseInstruction = false
                showingExerciseWorkout = true
            }
        }
        .fullScreenCover(isPresented: $showingExerciseWorkout) {
            ExerciseWorkoutView(timerManager: timerManager)
                .onDisappear {
                    timerManager.dismissFitnessView()
                }
        }
        .onChange(of: selectedTask) { _, newTask in
            timerManager.setCurrentTask(newTask)
        }
        .onChange(of: settingsManager.workSessionDuration) { _, _ in
            timerManager.updateSettings()
        }
        .onChange(of: settingsManager.shortBreakDuration) { _, _ in
            timerManager.updateSettings()
        }
        .onChange(of: settingsManager.longBreakDuration) { _, _ in
            timerManager.updateSettings()
        }
        .onChange(of: timerManager.shouldShowFitnessView) { _, shouldShow in
            if shouldShow {
                showingExerciseInstruction = true
            }
        }
    }
    
    private var headerView: some View {
        VStack(spacing: 8) {
            if let task = selectedTask {
                Button(action: { showingTaskSelector = true }) {
                    HStack {
                        Text(task.name ?? "Unnamed Task")
                            .font(.headline)
                            .foregroundColor(.white)
                        
                        Image(systemName: "chevron.down")
                            .foregroundColor(.white.opacity(0.7))
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 12)
                    .background(
                        Capsule()
                            .fill(Color.white.opacity(0.2))
                    )
                }
            } else {
                Button("Select a task") {
                    showingTaskSelector = true
                }
                .font(.headline)
                .foregroundColor(.white.opacity(0.8))
                .padding(.horizontal, 20)
                .padding(.vertical, 12)
                .background(
                    Capsule()
                        .stroke(Color.white.opacity(0.3), lineWidth: 1)
                )
            }
            
            Text("Focus on a process")
                .font(.title2)
                .fontWeight(.medium)
                .foregroundColor(.white)
            
            if let task = selectedTask {
                Text(task.name ?? "")
                    .font(.title)
                    .fontWeight(.bold)
                    .foregroundColor(.yellow)
            }
        }
    }
    
    private func timerDisplayView(geometry: GeometryProxy) -> some View {
        let size = min(geometry.size.width, geometry.size.height) * 0.6
        
        return ZStack {
            // Background circle
            Circle()
                .stroke(Color.white.opacity(0.2), lineWidth: 8)
                .frame(width: size, height: size)
            
            // Progress circle
            Circle()
                .trim(from: 0, to: timerManager.progress)
                .stroke(
                    Color.white,
                    style: StrokeStyle(lineWidth: 8, lineCap: .round)
                )
                .frame(width: size, height: size)
                .rotationEffect(.degrees(-90))
                .animation(.easeInOut(duration: 1), value: timerManager.progress)
            
            // Timer text
            VStack(spacing: 8) {
                Text(timerManager.formattedTimeRemaining)
                    .font(.system(size: 48, weight: .light, design: .monospaced))
                    .foregroundColor(.white)
                    .accessibilityLabel("Time remaining: \(timerManager.formattedTimeRemaining)")
                    .accessibilityAddTraits(.updatesFrequently)

                Text(timerManager.currentSessionType.displayName)
                    .font(.headline)
                    .foregroundColor(.white.opacity(0.8))
                    .accessibilityLabel("Current session: \(timerManager.currentSessionType.displayName)")
            }
            .accessibilityElement(children: .combine)
            .accessibilityLabel("Timer display. \(timerManager.formattedTimeRemaining) remaining for \(timerManager.currentSessionType.displayName)")
        }
    }
    
    private var timerControlsView: some View {
        HStack(spacing: 20) {
            // Reset button
            Button(action: timerManager.resetTimer) {
                Image(systemName: "arrow.clockwise")
                    .font(.title2)
                    .foregroundColor(.white)
                    .frame(width: 50, height: 50)
                    .background(Circle().fill(Color.white.opacity(0.2)))
            }
            .disabled(timerManager.currentState == .idle)
            
            Spacer()
            
            // Main control button
            Button(action: mainControlAction) {
                HStack {
                    Image(systemName: mainControlIcon)
                        .font(.title2)
                    Text(mainControlText)
                        .font(.headline)
                }
                .foregroundColor(.white)
                .padding(.horizontal, 30)
                .padding(.vertical, 15)
                .background(
                    Capsule()
                        .fill(Color.white.opacity(0.3))
                )
            }
            .accessibilityLabel("\(mainControlText) timer")
            .accessibilityHint("Double tap to \(mainControlText.lowercased()) the timer")
            
            Spacer()
            
            // Skip button
            Button(action: timerManager.skipSession) {
                Image(systemName: "forward.fill")
                    .font(.title2)
                    .foregroundColor(.white)
                    .frame(width: 50, height: 50)
                    .background(Circle().fill(Color.white.opacity(0.2)))
            }
            .disabled(timerManager.currentState == .idle)
        }
    }
    
    private var sessionTypeSelectorView: some View {
        HStack(spacing: 15) {
            ForEach(SessionType.allCases, id: \.self) { sessionType in
                Button(action: {
                    if timerManager.currentState == .idle {
                        timerManager.startSession(type: sessionType)
                    }
                }) {
                    VStack(spacing: 4) {
                        Text(sessionType.emoji)
                            .font(.title2)
                        Text(sessionType.displayName)
                            .font(.caption)
                            .multilineTextAlignment(.center)
                    }
                    .foregroundColor(timerManager.currentSessionType == sessionType ? .yellow : .white.opacity(0.7))
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(timerManager.currentSessionType == sessionType ? 
                                  Color.white.opacity(0.2) : Color.clear)
                    )
                }
                .disabled(timerManager.currentState != .idle)
            }
        }
    }
    
    private var mainControlIcon: String {
        switch timerManager.currentState {
        case .idle:
            return "play.fill"
        case .running:
            return "pause.fill"
        case .paused:
            return "play.fill"
        case .completed:
            return "checkmark"
        }
    }
    
    private var mainControlText: String {
        switch timerManager.currentState {
        case .idle:
            return "Start"
        case .running:
            return "Pause"
        case .paused:
            return "Resume"
        case .completed:
            return "Next"
        }
    }
    
    private func mainControlAction() {
        switch timerManager.currentState {
        case .idle:
            timerManager.startTimer()
        case .running:
            timerManager.pauseTimer()
        case .paused:
            timerManager.resumeTimer()
        case .completed:
            timerManager.startNextSession()
        }
    }
}

#Preview {
    TimerView()
        .environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
}
