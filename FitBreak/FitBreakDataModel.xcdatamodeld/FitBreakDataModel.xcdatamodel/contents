<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<model type="com.apple.IDECoreDataModeler.DataModel" documentVersion="1.0" lastSavedToolsVersion="22758" systemVersion="23F79" minimumToolsVersion="Automatic" sourceLanguage="Swift" usedWithSwiftData="YES" userDefinedModelVersionIdentifier="">
    <entity name="PomodoroSession" representedClassName="PomodoroSession" syncable="YES" codeGenerationType="class">
        <attribute name="completedAt" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="duration" optional="YES" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="sessionType" optional="YES" attributeType="String"/>
        <attribute name="wasCompleted" optional="YES" attributeType="Boolean" usesScalarValueType="YES"/>
        <relationship name="task" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="Task" inverseName="sessions" inverseEntity="Task"/>
    </entity>
    <entity name="Task" representedClassName="Task" syncable="YES" codeGenerationType="class">
        <attribute name="createdAt" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="isCompleted" optional="YES" attributeType="Boolean" usesScalarValueType="YES"/>
        <attribute name="name" optional="YES" attributeType="String"/>
        <attribute name="taskDescription" optional="YES" attributeType="String"/>
        <attribute name="updatedAt" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <relationship name="sessions" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="PomodoroSession" inverseName="task" inverseEntity="PomodoroSession"/>
    </entity>
    <entity name="UserSettings" representedClassName="UserSettings" syncable="YES" codeGenerationType="class">
        <attribute name="autoStartBreaks" optional="YES" attributeType="Boolean" usesScalarValueType="YES"/>
        <attribute name="autoStartSessions" optional="YES" attributeType="Boolean" usesScalarValueType="YES"/>
        <attribute name="fitnessFeatureEnabled" optional="YES" attributeType="Boolean" defaultValueString="YES" usesScalarValueType="YES"/>
        <attribute name="longBreakDuration" optional="YES" attributeType="Integer 32" defaultValueString="900" usesScalarValueType="YES"/>
        <attribute name="notificationsEnabled" optional="YES" attributeType="Boolean" usesScalarValueType="YES"/>
        <attribute name="pomodorosUntilLongBreak" optional="YES" attributeType="Integer 32" defaultValueString="4" usesScalarValueType="YES"/>
        <attribute name="selectedSoundName" optional="YES" attributeType="String"/>
        <attribute name="shortBreakDuration" optional="YES" attributeType="Integer 32" defaultValueString="300" usesScalarValueType="YES"/>
        <attribute name="workSessionDuration" optional="YES" attributeType="Integer 32" defaultValueString="1500" usesScalarValueType="YES"/>
    </entity>
</model>
