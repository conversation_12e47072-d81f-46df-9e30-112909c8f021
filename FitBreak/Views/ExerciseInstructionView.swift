//
//  ExerciseInstructionView.swift
//  FitBreak
//
//  Created by <PERSON> on 16/09/2025.
//

import SwiftUI
import AVKit
import Combine

struct ExerciseInstructionView: View {
    @StateObject private var mediaManager = MediaPlayerManager()
    @Environment(\.dismiss) private var dismiss
    
    let onStartWorkout: () -> Void
    
    @State private var showingVideo = false
    @State private var hasStartedAudio = false
    @State private var cancellables = Set<AnyCancellable>()
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // Background gradient matching app theme
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color.purple.opacity(0.8),
                        Color.purple.opacity(0.6),
                        Color.blue.opacity(0.4)
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
                
                VStack(spacing: 30) {
                    // Header
                    headerView
                    
                    Spacer()
                    
                    // Exercise instruction content
                    instructionContentView(geometry: geometry)
                    
                    Spacer()
                    
                    // Start workout button
                    startWorkoutButton
                    
                    Spacer()
                }
                .padding()
            }
        }
        .preferredColorScheme(.dark)
        .onAppear {
            setupExerciseInstruction()
        }
        .onDisappear {
            mediaManager.stopAudio()
            mediaManager.stopVideo()
        }
        .accessibilityElement(children: .contain)
        .accessibilityLabel("Exercise instruction screen")
        .accessibilityHint("Listen to instructions and watch demonstration video before starting workout")
    }
    
    // MARK: - Header View
    
    private var headerView: some View {
        VStack(spacing: 8) {
            HStack {
                Button(action: {
                    dismiss()
                }) {
                    Image(systemName: "xmark")
                        .font(.title2)
                        .foregroundColor(.white)
                        .frame(width: 44, height: 44)
                        .background(Circle().fill(Color.white.opacity(0.2)))
                }
                .accessibilityLabel("Close exercise instructions")
                .accessibilityHint("Double tap to return to timer")
                
                Spacer()
            }
            
            Text("Time for Exercise!")
                .font(.largeTitle)
                .fontWeight(.bold)
                .foregroundColor(.white)
                .multilineTextAlignment(.center)
                .accessibilityAddTraits(.isHeader)
            
            Text("Let's do some jumping jacks to energize your break")
                .font(.headline)
                .foregroundColor(.white.opacity(0.8))
                .multilineTextAlignment(.center)
                .padding(.horizontal)
        }
    }
    
    // MARK: - Instruction Content
    
    private func instructionContentView(geometry: GeometryProxy) -> some View {
        VStack(spacing: 24) {
            // Exercise title and emoji
            VStack(spacing: 12) {
                Text("🤸‍♀️")
                    .font(.system(size: 60))
                    .accessibilityHidden(true)
                
                Text("Jumping Jacks")
                    .font(.title)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                    .accessibilityAddTraits(.isHeader)
            }
            
            // Video demonstration
            videoPlayerView(geometry: geometry)
            
            // Exercise instructions
            instructionTextView
            
            // Audio status indicator
            audioStatusView
        }
    }
    
    private func videoPlayerView(geometry: GeometryProxy) -> some View {
        Group {
            if let player = mediaManager.player {
                VideoPlayer(player: player)
                    .frame(height: min(geometry.size.height * 0.3, 250))
                    .cornerRadius(16)
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(Color.white.opacity(0.3), lineWidth: 2)
                    )
                    .accessibilityLabel("Jumping jacks demonstration video")
                    .accessibilityHint("Video showing proper jumping jack form and technique")
            } else {
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color.white.opacity(0.1))
                    .frame(height: min(geometry.size.height * 0.3, 250))
                    .overlay(
                        VStack {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            Text("Loading video...")
                                .font(.caption)
                                .foregroundColor(.white.opacity(0.7))
                                .padding(.top, 8)
                        }
                    )
                    .accessibilityLabel("Loading demonstration video")
            }
        }
    }
    
    private var instructionTextView: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("How to do jumping jacks:")
                .font(.headline)
                .foregroundColor(.white)
                .accessibilityAddTraits(.isHeader)
            
            VStack(alignment: .leading, spacing: 12) {
                InstructionStep(
                    number: "1",
                    text: "Stand with feet together, arms at your sides"
                )
                
                InstructionStep(
                    number: "2",
                    text: "Jump while spreading legs shoulder-width apart"
                )
                
                InstructionStep(
                    number: "3",
                    text: "Simultaneously raise arms overhead"
                )
                
                InstructionStep(
                    number: "4",
                    text: "Jump back to starting position"
                )
                
                InstructionStep(
                    number: "5",
                    text: "Repeat at a comfortable pace"
                )
            }
        }
        .padding(.horizontal)
        .accessibilityElement(children: .contain)
        .accessibilityLabel("Exercise instructions")
    }
    
    private var audioStatusView: some View {
        HStack(spacing: 12) {
            Image(systemName: mediaManager.isAudioPlaying ? "speaker.wave.2.fill" : "speaker.fill")
                .foregroundColor(.white.opacity(0.7))
                .font(.title3)
            
            Text(audioStatusText)
                .font(.caption)
                .foregroundColor(.white.opacity(0.7))
            
            if case .loading = mediaManager.audioState {
                ProgressView()
                    .progressViewStyle(CircularProgressViewStyle(tint: .white.opacity(0.7)))
                    .scaleEffect(0.8)
            }
        }
        .accessibilityElement(children: .combine)
        .accessibilityLabel("Audio status: \(audioStatusText)")
    }
    
    private var audioStatusText: String {
        switch mediaManager.audioState {
        case .idle:
            return "Audio ready"
        case .loading:
            return "Loading audio..."
        case .ready:
            return "Audio loaded"
        case .playing:
            return "Playing instructions"
        case .paused:
            return "Audio paused"
        case .finished:
            return "Instructions complete"
        case .error:
            return "Audio unavailable"
        }
    }
    
    // MARK: - Start Workout Button
    
    private var startWorkoutButton: some View {
        Button(action: {
            // Announce transition for VoiceOver users
            AccessibilityHelper.announce("Starting workout")
            onStartWorkout()
        }) {
            HStack(spacing: 12) {
                Image(systemName: "play.fill")
                    .font(.title2)
                
                Text("Start Workout")
                    .font(.title2)
                    .fontWeight(.semibold)
            }
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .frame(height: 56)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color.green)
                    .shadow(color: .black.opacity(0.3), radius: 8, x: 0, y: 4)
            )
        }
        .padding(.horizontal)
        .accessibilityLabel("Start workout")
        .accessibilityHint("Double tap to begin jumping jacks exercise with timer")
        .accessibilityAddTraits(.isButton)
    }
    
    // MARK: - Setup
    
    private func setupExerciseInstruction() {
        // Load and play audio instructions
        mediaManager.loadAudio(filename: "jumping-jacks-voice-over")
        
        // Load video demonstration
        mediaManager.loadVideo(filename: "jumping-jacks")
        
        // Start audio and video when both are ready
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            if !hasStartedAudio {
                hasStartedAudio = true
                mediaManager.playAudio()
                
                // Start video with looping until audio finishes
                mediaManager.playVideo(loop: true)
                
                // Stop video when audio finishes
                observeAudioCompletion()
            }
        }
        
        // Announce screen for VoiceOver users
        AccessibilityHelper.announceScreenChange(
            "Exercise instruction screen. Listen to the instructions and watch the demonstration video.",
            delay: 1.0
        )
    }
    
    private func observeAudioCompletion() {
        // Monitor audio state changes
        Timer.publish(every: 0.5, on: .main, in: .common)
            .autoconnect()
            .sink { _ in
                if case .finished = mediaManager.audioState {
                    // Audio finished, stop video looping but keep it visible
                    mediaManager.stopVideo()
                    
                    // Announce completion for VoiceOver users
                    AccessibilityHelper.announce("Instructions complete. Ready to start workout.")
                }
            }
            .store(in: &cancellables)
    }
}

// MARK: - Instruction Step Component

struct InstructionStep: View {
    let number: String
    let text: String
    
    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            Text(number)
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(.green)
                .frame(width: 24, height: 24)
                .background(Circle().fill(Color.white.opacity(0.2)))
                .accessibilityHidden(true)
            
            Text(text)
                .font(.body)
                .foregroundColor(.white.opacity(0.9))
                .fixedSize(horizontal: false, vertical: true)
        }
        .exerciseStepAccessibility(stepNumber: number, instruction: text)
    }
}

#Preview {
    ExerciseInstructionView {
        print("Start workout tapped")
    }
}
