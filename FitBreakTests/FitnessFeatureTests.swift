//
//  FitnessFeatureTests.swift
//  FitBreakTests
//
//  Created by <PERSON> on 16/09/2025.
//

import XCTest
import CoreData
@testable import FitBreak

final class FitnessFeatureTests: XCTestCase {
    var persistenceController: PersistenceController!
    var settingsManager: SettingsManager!
    var timerManager: TimerManager!
    
    override func setUpWithError() throws {
        persistenceController = PersistenceController(inMemory: true)
        settingsManager = SettingsManager()
        timerManager = TimerManager()
    }
    
    override func tearDownWithError() throws {
        persistenceController = nil
        settingsManager = nil
        timerManager = nil
    }
    
    // MARK: - Settings Tests
    
    func testFitnessFeatureSettingDefault() throws {
        // Test that fitness feature is enabled by default
        XCTAssertTrue(settingsManager.fitnessFeatureEnabled)
    }
    
    func testFitnessFeatureSettingToggle() throws {
        // Test toggling fitness feature setting
        let initialValue = settingsManager.fitnessFeatureEnabled
        settingsManager.fitnessFeatureEnabled = !initialValue
        XCTAssertEqual(settingsManager.fitnessFeatureEnabled, !initialValue)
    }
    
    func testUserSettingsPersistence() throws {
        let userSettings = persistenceController.getUserSettings()
        
        // Test default value
        XCTAssertTrue(userSettings.fitnessFeatureEnabled)
        
        // Test updating value
        userSettings.fitnessFeatureEnabled = false
        persistenceController.updateUserSettings(userSettings)
        
        // Fetch again and verify persistence
        let updatedSettings = persistenceController.getUserSettings()
        XCTAssertFalse(updatedSettings.fitnessFeatureEnabled)
    }
    
    // MARK: - Timer Integration Tests
    
    func testTimerManagerFitnessViewTrigger() throws {
        // Enable fitness feature
        let userSettings = persistenceController.getUserSettings()
        userSettings.fitnessFeatureEnabled = true
        persistenceController.updateUserSettings(userSettings)
        
        // Start a work session
        timerManager.startSession(.work)
        XCTAssertEqual(timerManager.currentSessionType, .work)
        XCTAssertFalse(timerManager.shouldShowFitnessView)
        
        // Complete the work session (this should trigger short break)
        timerManager.completeCurrentSession(wasSkipped: false)
        
        // Note: In a real test, we'd need to wait for the timer to complete
        // For now, we just verify the session type changes
        XCTAssertEqual(timerManager.currentState, .completed)
    }
    
    func testFitnessViewNotTriggeredWhenDisabled() throws {
        // Disable fitness feature
        let userSettings = persistenceController.getUserSettings()
        userSettings.fitnessFeatureEnabled = false
        persistenceController.updateUserSettings(userSettings)
        
        // Verify fitness view is not triggered
        XCTAssertFalse(timerManager.shouldShowFitnessView)
    }
    
    func testDismissFitnessView() throws {
        // Set fitness view to show
        timerManager.shouldShowFitnessView = true
        XCTAssertTrue(timerManager.shouldShowFitnessView)
        
        // Dismiss it
        timerManager.dismissFitnessView()
        XCTAssertFalse(timerManager.shouldShowFitnessView)
    }
    
    // MARK: - Media Player Tests
    
    func testMediaPlayerInitialization() throws {
        let mediaManager = MediaPlayerManager()
        
        // Test initial states
        XCTAssertEqual(mediaManager.audioState, .idle)
        XCTAssertEqual(mediaManager.videoState, .idle)
        XCTAssertEqual(mediaManager.audioProgress, 0.0)
        XCTAssertEqual(mediaManager.audioCurrentTime, 0.0)
        XCTAssertEqual(mediaManager.audioDuration, 0.0)
        XCTAssertFalse(mediaManager.isAudioPlaying)
        XCTAssertFalse(mediaManager.isVideoPlaying)
    }
    
    func testMediaPlayerFileNotFound() throws {
        let mediaManager = MediaPlayerManager()
        
        // Test loading non-existent audio file
        mediaManager.loadAudio(filename: "non-existent-file")
        
        if case .error(let error) = mediaManager.audioState {
            XCTAssertTrue(error is MediaPlayerError)
            if let mediaError = error as? MediaPlayerError {
                switch mediaError {
                case .fileNotFound(let filename):
                    XCTAssertEqual(filename, "non-existent-file.mp3")
                default:
                    XCTFail("Expected fileNotFound error")
                }
            }
        } else {
            XCTFail("Expected error state for non-existent file")
        }
    }
    
    func testMediaPlayerAudioLoading() throws {
        let mediaManager = MediaPlayerManager()
        
        // Test loading existing audio file
        mediaManager.loadAudio(filename: "jumping-jacks-voice-over")
        
        // Give it a moment to load
        let expectation = XCTestExpectation(description: "Audio loading")
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 1.0)
        
        // Check if audio loaded successfully or if file doesn't exist in test bundle
        XCTAssertTrue(mediaManager.audioState == .ready || mediaManager.audioState == .error(.fileNotFound("jumping-jacks-voice-over.mp3")))
    }
    
    func testMediaPlayerVideoLoading() throws {
        let mediaManager = MediaPlayerManager()
        
        // Test loading existing video file
        mediaManager.loadVideo(filename: "jumping-jacks")
        
        // Give it a moment to load
        let expectation = XCTestExpectation(description: "Video loading")
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 1.0)
        
        // Check if video loaded successfully or if file doesn't exist in test bundle
        XCTAssertTrue(mediaManager.videoState == .ready || mediaManager.videoState == .loading || mediaManager.videoState == .error(.fileNotFound("jumping-jacks.mp4")))
    }
    
    // MARK: - Accessibility Tests
    
    func testAccessibilityHelper() throws {
        // Test time formatting for accessibility
        let timeString = AccessibilityHelper.formatTimeForAccessibility(90)
        XCTAssertEqual(timeString, "1 minute and 30 seconds")
        
        let timeString2 = AccessibilityHelper.formatTimeForAccessibility(60)
        XCTAssertEqual(timeString2, "1 minute")
        
        let timeString3 = AccessibilityHelper.formatTimeForAccessibility(30)
        XCTAssertEqual(timeString3, "30 seconds")
        
        let timeString4 = AccessibilityHelper.formatTimeForAccessibility(120)
        XCTAssertEqual(timeString4, "2 minutes")
    }
    
    func testExerciseStepLabel() throws {
        let label = AccessibilityHelper.exerciseStepLabel(stepNumber: "1", instruction: "Stand with feet together")
        XCTAssertEqual(label, "Step 1: Stand with feet together")
    }
    
    func testMotivationalMessageLabel() throws {
        let message = "You're doing great! Keep moving! 💪"
        let label = AccessibilityHelper.motivationalMessageLabel(message)
        XCTAssertEqual(label, "Motivation: You're doing great! Keep moving!")
    }
    
    // MARK: - Performance Tests
    
    func testMediaPlayerPerformance() throws {
        measure {
            let mediaManager = MediaPlayerManager()
            mediaManager.loadAudio(filename: "jumping-jacks-voice-over")
            mediaManager.loadVideo(filename: "jumping-jacks")
        }
    }
    
    func testSettingsManagerPerformance() throws {
        measure {
            let settings = SettingsManager()
            settings.fitnessFeatureEnabled = true
            settings.fitnessFeatureEnabled = false
            settings.fitnessFeatureEnabled = true
        }
    }
}
