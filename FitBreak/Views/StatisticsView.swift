//
//  StatisticsView.swift
//  FitBreak
//
//  Created by <PERSON> on 16/09/2025.
//

import SwiftUI
import CoreData

#if canImport(Charts)
import Charts
#endif

struct StatisticsView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @StateObject private var statisticsManager = StatisticsManager()
    @State private var selectedTimeframe: TimeFrame = .week
    
    enum TimeFrame: String, CaseIterable {
        case day = "Today"
        case week = "This Week"
        case month = "This Month"
        case year = "This Year"
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Time frame selector
                    timeFrameSelector
                    
                    // Key metrics cards
                    metricsCardsView
                    
                    // Charts section
                    chartsSection
                    
                    // Streak information
                    streakSection
                    
                    // Recent sessions
                    recentSessionsSection
                }
                .padding()
            }
            .navigationTitle("Statistics")
            .onAppear {
                statisticsManager.loadStatistics(for: selectedTimeframe)
            }
            .onChange(of: selectedTimeframe) { _, newTimeframe in
                statisticsManager.loadStatistics(for: newTimeframe)
            }
        }
    }
    
    private var timeFrameSelector: some View {
        Picker("Time Frame", selection: $selectedTimeframe) {
            ForEach(TimeFrame.allCases, id: \.self) { timeframe in
                Text(timeframe.rawValue).tag(timeframe)
            }
        }
        .pickerStyle(.segmented)
    }
    
    private var metricsCardsView: some View {
        LazyVGrid(columns: [
            GridItem(.flexible()),
            GridItem(.flexible())
        ], spacing: 16) {
            MetricCard(
                title: "Completed Sessions",
                value: "\(statisticsManager.completedSessions)",
                icon: "checkmark.circle.fill",
                color: .green
            )
            
            MetricCard(
                title: "Focus Time",
                value: statisticsManager.totalFocusTimeFormatted,
                icon: "clock.fill",
                color: .blue
            )
            
            MetricCard(
                title: "Current Streak",
                value: "\(statisticsManager.currentStreak) days",
                icon: "flame.fill",
                color: .orange
            )
            
            MetricCard(
                title: "Average Session",
                value: statisticsManager.averageSessionTimeFormatted,
                icon: "chart.bar.fill",
                color: .purple
            )
        }
    }
    
    private var chartsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Activity Overview")
                .font(.headline)
                .padding(.horizontal)
            
            if #available(iOS 16.0, *) {
                Chart(statisticsManager.dailyStats) { stat in
                    BarMark(
                        x: .value("Date", stat.date, unit: .day),
                        y: .value("Sessions", stat.completedSessions)
                    )
                    .foregroundStyle(.blue)
                }
                .frame(height: 200)
                .padding(.horizontal)
            } else {
                // Fallback for older iOS versions
                SimpleBarChart(data: statisticsManager.dailyStats)
                    .frame(height: 200)
                    .padding(.horizontal)
            }
        }
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray6))
        )
    }
    
    private var streakSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Streak Information")
                .font(.headline)
            
            HStack {
                VStack(alignment: .leading) {
                    Text("Current Streak")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text("\(statisticsManager.currentStreak) days")
                        .font(.title2)
                        .fontWeight(.bold)
                }
                
                Spacer()
                
                VStack(alignment: .trailing) {
                    Text("Best Streak")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text("\(statisticsManager.bestStreak) days")
                        .font(.title2)
                        .fontWeight(.bold)
                }
            }
            
            if statisticsManager.currentStreak > 0 {
                Text("Keep it up! You're on a \(statisticsManager.currentStreak)-day streak.")
                    .font(.caption)
                    .foregroundColor(.secondary)
            } else {
                Text("Complete a Pomodoro session today to start your streak!")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray6))
        )
    }
    
    private var recentSessionsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Recent Sessions")
                .font(.headline)
            
            if statisticsManager.recentSessions.isEmpty {
                Text("No sessions completed yet")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .padding()
            } else {
                ForEach(statisticsManager.recentSessions.prefix(5), id: \.self) { session in
                    RecentSessionRow(session: session)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray6))
        )
    }
}

struct MetricCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 8) {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(color)
                    .font(.title2)
                
                Spacer()
            }
            
            VStack(alignment: .leading, spacing: 4) {
                Text(value)
                    .font(.title2)
                    .fontWeight(.bold)
                
                Text(title)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .frame(maxWidth: .infinity, alignment: .leading)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray6))
        )
    }
}

struct RecentSessionRow: View {
    let session: PomodoroSession
    
    var body: some View {
        HStack {
            Image(systemName: sessionIcon)
                .foregroundColor(sessionColor)
                .frame(width: 20)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(sessionTypeText)
                    .font(.caption)
                    .fontWeight(.medium)
                
                if let task = session.task {
                    Text(task.name ?? "Unnamed Task")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            }
            
            Spacer()
            
            VStack(alignment: .trailing, spacing: 2) {
                Text(durationText)
                    .font(.caption)
                    .fontWeight(.medium)
                
                if let completedAt = session.completedAt {
                    Text(completedAt, style: .relative)
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding(.vertical, 4)
    }
    
    private var sessionIcon: String {
        switch session.sessionType {
        case "work":
            return "circle.fill"
        case "shortBreak":
            return "pause.circle.fill"
        case "longBreak":
            return "stop.circle.fill"
        default:
            return "circle"
        }
    }
    
    private var sessionColor: Color {
        switch session.sessionType {
        case "work":
            return .blue
        case "shortBreak":
            return .green
        case "longBreak":
            return .purple
        default:
            return .gray
        }
    }
    
    private var sessionTypeText: String {
        switch session.sessionType {
        case "work":
            return "Focus Session"
        case "shortBreak":
            return "Short Break"
        case "longBreak":
            return "Long Break"
        default:
            return "Session"
        }
    }
    
    private var durationText: String {
        let minutes = Int(session.duration) / 60
        return "\(minutes) min"
    }
}

// Simple bar chart for iOS versions before 16.0
struct SimpleBarChart: View {
    let data: [DailyStatistic]
    
    var body: some View {
        HStack(alignment: .bottom, spacing: 4) {
            ForEach(data) { stat in
                VStack {
                    Rectangle()
                        .fill(.blue)
                        .frame(width: 20, height: CGFloat(stat.completedSessions * 10))
                    
                    Text("\(Calendar.current.component(.day, from: stat.date))")
                        .font(.caption2)
                }
            }
        }
    }
}

#Preview {
    StatisticsView()
        .environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
}
