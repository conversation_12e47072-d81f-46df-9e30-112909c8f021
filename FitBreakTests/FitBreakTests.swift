//
//  FitBreakTests.swift
//  FitBreakTests
//
//  Created by <PERSON> on 16/09/2025.
//

import XCTest
import CoreData
@testable import FitBreak

final class FitBreakTests: XCTestCase {
    var persistenceController: PersistenceController!
    var settingsManager: SettingsManager!
    var statisticsManager: StatisticsManager!
    
    override func setUpWithError() throws {
        persistenceController = PersistenceController(inMemory: true)
        settingsManager = SettingsManager()
        statisticsManager = StatisticsManager()
    }
    
    override func tearDownWithError() throws {
        persistenceController = nil
        settingsManager = nil
        statisticsManager = nil
    }
    
    // MARK: - Core Data Tests
    
    func testTaskCreation() throws {
        let task = persistenceController.createTask(name: "Test Task", description: "Test Description")
        
        XCTAssertEqual(task.name, "Test Task")
        XCTAssertEqual(task.taskDescription, "Test Description")
        XCTAssertFalse(task.isCompleted)
        XCTAssertNotNil(task.createdAt)
        XCTAssertNotNil(task.updatedAt)
    }
    
    func testTaskUpdate() throws {
        let task = persistenceController.createTask(name: "Original Name")
        
        persistenceController.updateTask(task, name: "Updated Name", isCompleted: true)
        
        XCTAssertEqual(task.name, "Updated Name")
        XCTAssertTrue(task.isCompleted)
    }
    
    func testTaskDeletion() throws {
        let task = persistenceController.createTask(name: "Task to Delete")
        let context = persistenceController.container.viewContext
        
        persistenceController.deleteTask(task)
        
        let request: NSFetchRequest<Task> = Task.fetchRequest()
        let tasks = try context.fetch(request)
        
        XCTAssertTrue(tasks.isEmpty)
    }
    
    func testPomodoroSessionCreation() throws {
        let task = persistenceController.createTask(name: "Test Task")
        let session = persistenceController.createPomodoroSession(
            type: "work",
            duration: 1500,
            task: task,
            completed: true
        )
        
        XCTAssertEqual(session.sessionType, "work")
        XCTAssertEqual(session.duration, 1500)
        XCTAssertTrue(session.wasCompleted)
        XCTAssertEqual(session.task, task)
        XCTAssertNotNil(session.completedAt)
    }
    
    // MARK: - Settings Tests
    
    func testSettingsValidation() throws {
        XCTAssertTrue(settingsManager.isValidWorkDuration(25))
        XCTAssertFalse(settingsManager.isValidWorkDuration(10))
        XCTAssertFalse(settingsManager.isValidWorkDuration(70))
        
        XCTAssertTrue(settingsManager.isValidShortBreakDuration(5))
        XCTAssertFalse(settingsManager.isValidShortBreakDuration(2))
        XCTAssertFalse(settingsManager.isValidShortBreakDuration(20))
        
        XCTAssertTrue(settingsManager.isValidLongBreakDuration(15))
        XCTAssertFalse(settingsManager.isValidLongBreakDuration(10))
        XCTAssertFalse(settingsManager.isValidLongBreakDuration(50))
        
        XCTAssertTrue(settingsManager.isValidPomodoroCount(4))
        XCTAssertFalse(settingsManager.isValidPomodoroCount(1))
        XCTAssertFalse(settingsManager.isValidPomodoroCount(10))
    }
    
    func testSettingsExportImport() throws {
        // Set custom values
        settingsManager.workSessionDuration = 30
        settingsManager.shortBreakDuration = 10
        settingsManager.autoStartSessions = true
        
        // Export settings
        let exportedData = settingsManager.exportSettings()
        
        // Reset to defaults
        settingsManager.resetToDefaults()
        
        // Verify reset
        XCTAssertEqual(settingsManager.workSessionDuration, 25)
        XCTAssertEqual(settingsManager.shortBreakDuration, 5)
        XCTAssertFalse(settingsManager.autoStartSessions)
        
        // Import settings
        settingsManager.importSettings(from: exportedData)
        
        // Verify import
        XCTAssertEqual(settingsManager.workSessionDuration, 30)
        XCTAssertEqual(settingsManager.shortBreakDuration, 10)
        XCTAssertTrue(settingsManager.autoStartSessions)
    }
    
    // MARK: - Timer Tests
    
    func testTimerStateTransitions() throws {
        let timerManager = TimerManager()
        
        // Initial state
        XCTAssertEqual(timerManager.currentState, .idle)
        
        // Start timer
        timerManager.startTimer()
        XCTAssertEqual(timerManager.currentState, .running)
        
        // Pause timer
        timerManager.pauseTimer()
        XCTAssertEqual(timerManager.currentState, .paused)
        
        // Resume timer
        timerManager.resumeTimer()
        XCTAssertEqual(timerManager.currentState, .running)
        
        // Reset timer
        timerManager.resetTimer()
        XCTAssertEqual(timerManager.currentState, .idle)
    }
    
    func testSessionTypeTransitions() throws {
        let timerManager = TimerManager()
        
        // Start with work session
        XCTAssertEqual(timerManager.currentSessionType, .work)
        
        // Simulate completing 3 work sessions
        timerManager.completedPomodoros = 3
        timerManager.startNextSession()
        XCTAssertEqual(timerManager.currentSessionType, .shortBreak)
        
        // Complete short break
        timerManager.startNextSession()
        XCTAssertEqual(timerManager.currentSessionType, .work)
        
        // Complete 4th work session (should trigger long break)
        timerManager.completedPomodoros = 4
        timerManager.startNextSession()
        XCTAssertEqual(timerManager.currentSessionType, .longBreak)
    }
    
    func testTimeFormatting() throws {
        let timerManager = TimerManager()
        
        XCTAssertEqual(timerManager.formattedTime(0), "00:00")
        XCTAssertEqual(timerManager.formattedTime(60), "01:00")
        XCTAssertEqual(timerManager.formattedTime(125), "02:05")
        XCTAssertEqual(timerManager.formattedTime(3661), "61:01")
    }
    
    // MARK: - Performance Tests
    
    func testTaskFetchPerformance() throws {
        // Create many tasks
        for i in 0..<1000 {
            _ = persistenceController.createTask(name: "Task \(i)")
        }
        
        measure {
            let context = persistenceController.container.viewContext
            let request: NSFetchRequest<Task> = Task.fetchRequest()
            request.sortDescriptors = [NSSortDescriptor(keyPath: \Task.createdAt, ascending: false)]
            
            do {
                _ = try context.fetch(request)
            } catch {
                XCTFail("Failed to fetch tasks: \(error)")
            }
        }
    }
    
    func testStatisticsCalculationPerformance() throws {
        // Create many sessions
        let task = persistenceController.createTask(name: "Performance Test Task")
        
        for i in 0..<500 {
            _ = persistenceController.createPomodoroSession(
                type: "work",
                duration: 1500,
                task: task,
                completed: true
            )
        }
        
        measure {
            statisticsManager.loadStatistics(for: .month)
        }
    }
}

// MARK: - Timer Manager Tests

@MainActor
final class TimerManagerTests: XCTestCase {
    var timerManager: TimerManager!
    
    override func setUpWithError() throws {
        timerManager = TimerManager()
    }
    
    override func tearDownWithError() throws {
        timerManager = nil
    }
    
    func testInitialState() throws {
        XCTAssertEqual(timerManager.currentState, .idle)
        XCTAssertEqual(timerManager.currentSessionType, .work)
        XCTAssertEqual(timerManager.completedPomodoros, 0)
        XCTAssertEqual(timerManager.progress, 0.0)
    }
    
    func testSessionDurationSettings() throws {
        timerManager.startSession(type: .work)
        XCTAssertGreaterThan(timerManager.totalDuration, 0)

        timerManager.startSession(type: .shortBreak)
        XCTAssertGreaterThan(timerManager.totalDuration, 0)

        timerManager.startSession(type: .longBreak)
        XCTAssertGreaterThan(timerManager.totalDuration, 0)
    }
}
