//
//  ExerciseWorkoutView.swift
//  FitBreak
//
//  Created by <PERSON> on 16/09/2025.
//

import SwiftUI
import AVKit
import Combine

struct ExerciseWorkoutView: View {
    @ObservedObject var timerManager: TimerManager
    @StateObject private var mediaManager = MediaPlayerManager()
    @Environment(\.dismiss) private var dismiss
    
    @State private var currentMotivationIndex = 0
    @State private var motivationTimer: Timer?
    @State private var cancellables = Set<AnyCancellable>()
    
    // Motivational messages that rotate during workout
    private let motivationalMessages = [
        "You're doing great! Keep moving! 💪",
        "Feel that energy boost! ⚡",
        "Every jump counts! 🌟",
        "You're stronger than you think! 🔥",
        "Break time = active time! 🎯",
        "Your body will thank you! ❤️",
        "Movement is medicine! 🏃‍♀️",
        "You've got this! 🚀",
        "Energize your mind and body! ✨",
        "Small breaks, big benefits! 🌈"
    ]
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // Background gradient matching app theme
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color.purple.opacity(0.8),
                        Color.purple.opacity(0.6),
                        Color.blue.opacity(0.4)
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
                
                VStack(spacing: 20) {
                    // Header with close button
                    headerView
                    
                    // Timer display
                    timerDisplayView
                    
                    // Video player
                    videoPlayerView(geometry: geometry)
                    
                    Spacer()
                    
                    // Motivational message
                    motivationalMessageView
                    
                    // Control buttons
                    controlButtonsView
                    
                    Spacer()
                }
                .padding()
            }
        }
        .preferredColorScheme(.dark)
        .onAppear {
            setupWorkout()
        }
        .onDisappear {
            cleanup()
        }
        .accessibilityElement(children: .contain)
        .accessibilityLabel("Exercise workout screen")
    }
    
    // MARK: - Header View
    
    private var headerView: some View {
        HStack {
            Button(action: {
                // Announce for VoiceOver users
                AccessibilityHelper.announce("Ending workout early")
                dismiss()
            }) {
                Image(systemName: "xmark")
                    .font(.title2)
                    .foregroundColor(.white)
                    .frame(width: 44, height: 44)
                    .background(Circle().fill(Color.white.opacity(0.2)))
            }
            .accessibilityLabel("End workout early")
            .accessibilityHint("Double tap to stop exercise and return to timer")
            
            Spacer()
            
            Text("Active Break")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.white)
                .accessibilityAddTraits(.isHeader)
            
            Spacer()
            
            // Placeholder for symmetry
            Color.clear
                .frame(width: 44, height: 44)
        }
    }
    
    // MARK: - Timer Display
    
    private var timerDisplayView: some View {
        VStack(spacing: 8) {
            Text("Time Remaining")
                .font(.caption)
                .foregroundColor(.white.opacity(0.7))
                .textCase(.uppercase)
                .tracking(1)
            
            Text(timerManager.formattedTimeRemaining)
                .font(.system(size: 48, weight: .light, design: .monospaced))
                .foregroundColor(.white)
                .dynamicTypeSize(minScale: 0.7, maxScale: 1.2)
                .timerDisplayAccessibility(timeRemaining: timerManager.timeRemaining, sessionType: timerManager.currentSessionType)
            
            // Progress bar
            ProgressView(value: timerManager.progress)
                .progressViewStyle(LinearProgressViewStyle(tint: .green))
                .frame(height: 8)
                .background(Color.white.opacity(0.2))
                .cornerRadius(4)
                .padding(.horizontal, 20)
                .accessibilityLabel("Break progress: \(Int(timerManager.progress * 100)) percent complete")
        }
        .padding(.vertical)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                )
        )
    }
    
    // MARK: - Video Player
    
    private func videoPlayerView(geometry: GeometryProxy) -> some View {
        Group {
            if let player = mediaManager.player {
                VideoPlayer(player: player)
                    .frame(height: min(geometry.size.height * 0.35, 280))
                    .cornerRadius(16)
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(Color.white.opacity(0.3), lineWidth: 2)
                    )
                    .accessibilityLabel("Jumping jacks demonstration video")
                    .accessibilityHint("Follow along with the exercise demonstration")
            } else {
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color.white.opacity(0.1))
                    .frame(height: min(geometry.size.height * 0.35, 280))
                    .overlay(
                        VStack {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            Text("Loading exercise video...")
                                .font(.caption)
                                .foregroundColor(.white.opacity(0.7))
                                .padding(.top, 8)
                        }
                    )
                    .accessibilityLabel("Loading exercise demonstration")
            }
        }
    }
    
    // MARK: - Motivational Message
    
    private var motivationalMessageView: some View {
        VStack(spacing: 12) {
            Text("💪")
                .font(.system(size: 32))
                .accessibilityHidden(true)
            
            Text(motivationalMessages[currentMotivationIndex])
                .font(.title3)
                .fontWeight(.medium)
                .foregroundColor(.white)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
                .motivationalMessageAccessibility(motivationalMessages[currentMotivationIndex])
        }
        .frame(minHeight: 80)
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.green.opacity(0.2))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.green.opacity(0.4), lineWidth: 1)
                )
        )
        .animation(.easeInOut(duration: 0.5), value: currentMotivationIndex)
    }
    
    // MARK: - Control Buttons
    
    private var controlButtonsView: some View {
        HStack(spacing: 20) {
            // Pause/Resume timer button
            Button(action: {
                if timerManager.currentState == .running {
                    timerManager.pauseTimer()
                    mediaManager.pauseVideo()
                    AccessibilityHelper.announce("Workout paused")
                } else if timerManager.currentState == .paused {
                    timerManager.resumeTimer()
                    mediaManager.playVideo(loop: true)
                    AccessibilityHelper.announce("Workout resumed")
                }
            }) {
                HStack(spacing: 8) {
                    Image(systemName: timerManager.currentState == .running ? "pause.fill" : "play.fill")
                        .font(.title3)
                    
                    Text(timerManager.currentState == .running ? "Pause" : "Resume")
                        .font(.headline)
                        .fontWeight(.medium)
                }
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .frame(height: 50)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.orange)
                )
            }
            .accessibilityLabel(timerManager.currentState == .running ? "Pause workout" : "Resume workout")
            .accessibilityHint("Double tap to \(timerManager.currentState == .running ? "pause" : "resume") the break timer and exercise")
            
            // Skip break button
            Button(action: {
                AccessibilityHelper.announce("Skipping break")
                timerManager.skipSession()
                dismiss()
            }) {
                HStack(spacing: 8) {
                    Image(systemName: "forward.fill")
                        .font(.title3)
                    
                    Text("Skip")
                        .font(.headline)
                        .fontWeight(.medium)
                }
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .frame(height: 50)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.red.opacity(0.8))
                )
            }
            .accessibilityLabel("Skip break")
            .accessibilityHint("Double tap to end break early and return to work session")
        }
        .padding(.horizontal)
    }
    
    // MARK: - Setup and Cleanup
    
    private func setupWorkout() {
        // Load and start video
        mediaManager.loadVideo(filename: "jumping-jacks")
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            mediaManager.playVideo(loop: true)
        }
        
        // Start motivational message rotation
        startMotivationRotation()
        
        // Monitor timer completion
        observeTimerCompletion()
        
        // Announce screen for VoiceOver users
        AccessibilityHelper.announceScreenChange(
            "Exercise workout started. Follow along with the video demonstration.",
            delay: 1.0
        )
    }
    
    private func startMotivationRotation() {
        motivationTimer = Timer.scheduledTimer(withTimeInterval: 8.0, repeats: true) { _ in
            withAnimation(.easeInOut(duration: 0.5)) {
                currentMotivationIndex = (currentMotivationIndex + 1) % motivationalMessages.count
            }
        }
    }
    
    private func observeTimerCompletion() {
        timerManager.$currentState
            .sink { state in
                if state == .completed {
                    // Break completed, dismiss workout view
                    AccessibilityHelper.announce(
                        "Break completed! Great job on staying active.",
                        delay: 1.0
                    )
                    DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                        dismiss()
                    }
                }
            }
            .store(in: &cancellables)
    }
    
    private func cleanup() {
        mediaManager.stopVideo()
        motivationTimer?.invalidate()
        motivationTimer = nil
        cancellables.removeAll()
    }
}

#Preview {
    ExerciseWorkoutView(timerManager: TimerManager())
}
