//
//  MediaPlayerManager.swift
//  FitBreak
//
//  Created by <PERSON> on 16/09/2025.
//

@preconcurrency import Foundation
@preconcurrency import AVFoundation
import Combine
import UIKit

enum MediaPlayerState: Equatable {
    case idle
    case loading
    case ready
    case playing
    case paused
    case finished
    case error(Error)

    static func == (lhs: MediaPlayerState, rhs: MediaPlayerState) -> Bo<PERSON> {
        switch (lhs, rhs) {
        case (.idle, .idle),
             (.loading, .loading),
             (.ready, .ready),
             (.playing, .playing),
             (.paused, .paused),
             (.finished, .finished):
            return true
        case (.error(let lhsError), .error(let rhsError)):
            return lhsError.localizedDescription == rhsError.localizedDescription
        default:
            return false
        }
    }
}

enum MediaPlayerError: LocalizedError {
    case fileNotFound(String)
    case invalidURL
    case playbackFailed(Error)
    case audioSessionSetupFailed(Error)
    
    var errorDescription: String? {
        switch self {
        case .fileNotFound(let filename):
            return "Media file not found: \(filename)"
        case .invalidURL:
            return "Invalid media URL"
        case .playbackFailed(let error):
            return "Playback failed: \(error.localizedDescription)"
        case .audioSessionSetupFailed(let error):
            return "Audio session setup failed: \(error.localizedDescription)"
        }
    }
}

@MainActor
class MediaPlayerManager: NSObject, ObservableObject {
    @Published var audioState: MediaPlayerState = .idle
    @Published var videoState: MediaPlayerState = .idle
    @Published var audioProgress: Double = 0.0
    @Published var videoDuration: TimeInterval = 0.0
    @Published var audioCurrentTime: TimeInterval = 0.0
    @Published var audioDuration: TimeInterval = 0.0
    
    private var audioPlayer: AVAudioPlayer?
    private var videoPlayer: AVPlayer?
    private var videoPlayerItem: AVPlayerItem?
    private var timeObserver: Any?
    private var cancellables = Set<AnyCancellable>()

    // Notification observers
    private var appDidEnterBackgroundObserver: NSObjectProtocol?
    private var appWillEnterForegroundObserver: NSObjectProtocol?

    // Nonisolated references for cleanup
    nonisolated private let cleanupQueue = DispatchQueue(label: "MediaPlayerCleanup", qos: .utility)
    
    override init() {
        super.init()
        setupAudioSession()
        setupAppLifecycleObservers()
    }
    
    deinit {
        // Capture references to resources that need cleanup
        let audioPlayerRef = audioPlayer
        let videoPlayerRef = videoPlayer
        let videoPlayerItemRef = videoPlayerItem
        let timeObserverRef = timeObserver
        let backgroundObserverRef = appDidEnterBackgroundObserver
        let foregroundObserverRef = appWillEnterForegroundObserver

        // Perform cleanup on a background queue to avoid blocking
        cleanupQueue.async {
            Self.performCleanup(
                audioPlayer: audioPlayerRef,
                videoPlayer: videoPlayerRef,
                videoPlayerItem: videoPlayerItemRef,
                timeObserver: timeObserverRef,
                backgroundObserver: backgroundObserverRef,
                foregroundObserver: foregroundObserverRef
            )
        }
    }
    
    // MARK: - Audio Session Setup
    
    private func setupAudioSession() {
        do {
            let audioSession = AVAudioSession.sharedInstance()
            try audioSession.setCategory(.playback, mode: .default, options: [.mixWithOthers])
            try audioSession.setActive(true)
        } catch {
            audioState = .error(MediaPlayerError.audioSessionSetupFailed(error))
        }
    }
    
    // MARK: - Audio Playback
    
    func loadAudio(filename: String, fileExtension: String = "mp3") {
        // Stop any existing audio first
        stopAudio()

        guard let url = Bundle.main.url(forResource: filename, withExtension: fileExtension) else {
            audioState = .error(MediaPlayerError.fileNotFound("\(filename).\(fileExtension)"))
            return
        }

        audioState = .loading

        do {
            audioPlayer = try AVAudioPlayer(contentsOf: url)
            audioPlayer?.delegate = self
            audioPlayer?.prepareToPlay()

            audioDuration = audioPlayer?.duration ?? 0.0
            audioState = .ready
        } catch {
            audioState = .error(MediaPlayerError.playbackFailed(error))
        }
    }
    
    func playAudio() {
        guard let audioPlayer = audioPlayer, audioState == .ready || audioState == .paused else {
            return
        }
        
        audioPlayer.play()
        audioState = .playing
        startAudioProgressTracking()
    }
    
    func pauseAudio() {
        guard let audioPlayer = audioPlayer, audioState == .playing else {
            return
        }
        
        audioPlayer.pause()
        audioState = .paused
        stopAudioProgressTracking()
    }
    
    func stopAudio() {
        audioPlayer?.stop()
        audioPlayer?.currentTime = 0
        audioState = .ready
        audioProgress = 0.0
        audioCurrentTime = 0.0
        stopAudioProgressTracking()
    }
    
    // MARK: - Video Playback
    
    func loadVideo(filename: String, fileExtension: String = "mp4") {
        // Stop any existing video first
        stopVideo()

        guard let url = Bundle.main.url(forResource: filename, withExtension: fileExtension) else {
            videoState = .error(MediaPlayerError.fileNotFound("\(filename).\(fileExtension)"))
            return
        }

        videoState = .loading

        videoPlayerItem = AVPlayerItem(url: url)
        videoPlayer = AVPlayer(playerItem: videoPlayerItem)

        // Mute video audio
        videoPlayer?.isMuted = true

        setupVideoObservers()

        videoPlayerItem?.publisher(for: \.status)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] status in
                switch status {
                case .readyToPlay:
                    self?.videoDuration = self?.videoPlayerItem?.duration.seconds ?? 0.0
                    self?.videoState = .ready
                case .failed:
                    if let error = self?.videoPlayerItem?.error {
                        self?.videoState = .error(MediaPlayerError.playbackFailed(error))
                    } else {
                        self?.videoState = .error(MediaPlayerError.playbackFailed(NSError(domain: "VideoPlayerError", code: -1, userInfo: [NSLocalizedDescriptionKey: "Unknown video playback error"])))
                    }
                default:
                    break
                }
            }
            .store(in: &cancellables)
    }
    
    func playVideo(loop: Bool = false) {
        guard let videoPlayer = videoPlayer,
              videoState == .ready || videoState == .paused,
              videoPlayerItem?.status == .readyToPlay else {
            return
        }

        if loop {
            setupVideoLooping()
        }

        videoPlayer.play()
        videoState = .playing
    }
    
    func pauseVideo() {
        guard let videoPlayer = videoPlayer, videoState == .playing else {
            return
        }
        
        videoPlayer.pause()
        videoState = .paused
    }
    
    func stopVideo() {
        videoPlayer?.pause()
        videoPlayer?.seek(to: .zero)
        videoState = .ready
        removeVideoLooping()
    }
    
    // MARK: - Video Looping
    
    private func setupVideoLooping() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(videoDidFinishPlaying),
            name: .AVPlayerItemDidPlayToEndTime,
            object: videoPlayerItem
        )
    }
    
    private func removeVideoLooping() {
        NotificationCenter.default.removeObserver(
            self,
            name: .AVPlayerItemDidPlayToEndTime,
            object: videoPlayerItem
        )
    }
    
    @objc private func videoDidFinishPlaying() {
        videoPlayer?.seek(to: .zero)
        videoPlayer?.play()
    }
    
    // MARK: - Progress Tracking
    
    private func startAudioProgressTracking() {
        Timer.publish(every: 0.1, on: .main, in: .common)
            .autoconnect()
            .sink { [weak self] _ in
                guard let self = self,
                      let audioPlayer = self.audioPlayer,
                      self.audioState == .playing else {
                    return
                }
                
                self.audioCurrentTime = audioPlayer.currentTime
                self.audioProgress = audioPlayer.currentTime / audioPlayer.duration
            }
            .store(in: &cancellables)
    }
    
    private func stopAudioProgressTracking() {
        cancellables.removeAll()
    }
    
    private func setupVideoObservers() {
        guard let videoPlayer = videoPlayer else { return }
        
        timeObserver = videoPlayer.addPeriodicTimeObserver(
            forInterval: CMTime(seconds: 0.1, preferredTimescale: 600),
            queue: .main
        ) { [weak self] _ in
            // Video progress tracking if needed
        }
    }
    
    // MARK: - App Lifecycle
    
    private func setupAppLifecycleObservers() {
        appDidEnterBackgroundObserver = NotificationCenter.default.addObserver(
            forName: UIApplication.didEnterBackgroundNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            DispatchQueue.main.async {
                self?.handleAppDidEnterBackground()
            }
        }
        
        appWillEnterForegroundObserver = NotificationCenter.default.addObserver(
            forName: UIApplication.willEnterForegroundNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            DispatchQueue.main.async {
                self?.handleAppWillEnterForeground()
            }
        }
    }
    
    private func handleAppDidEnterBackground() {
        // Pause video when app goes to background
        if videoState == .playing {
            pauseVideo()
        }
    }
    
    private func handleAppWillEnterForeground() {
        // Resume video if it was playing before backgrounding
        if videoState == .paused {
            playVideo(loop: true)
        }
    }
    
    // MARK: - Cleanup

    private func cleanup() {
        stopAudio()
        stopVideo()

        if let timeObserver = timeObserver {
            videoPlayer?.removeTimeObserver(timeObserver)
        }

        removeVideoLooping()

        if let observer = appDidEnterBackgroundObserver {
            NotificationCenter.default.removeObserver(observer)
        }

        if let observer = appWillEnterForegroundObserver {
            NotificationCenter.default.removeObserver(observer)
        }

        cancellables.removeAll()
    }

    // Static cleanup method that can be called from deinit with captured references
    nonisolated private static func performCleanup(
        audioPlayer: AVAudioPlayer?,
        videoPlayer: AVPlayer?,
        videoPlayerItem: AVPlayerItem?,
        timeObserver: Any?,
        backgroundObserver: NSObjectProtocol?,
        foregroundObserver: NSObjectProtocol?
    ) {
        // Stop audio playback
        audioPlayer?.stop()

        // Stop video playback
        videoPlayer?.pause()
        videoPlayer?.seek(to: .zero)

        // Remove time observer
        if let timeObserver = timeObserver {
            videoPlayer?.removeTimeObserver(timeObserver)
        }

        // Remove notification observers
        if let observer = backgroundObserver {
            NotificationCenter.default.removeObserver(observer)
        }

        if let observer = foregroundObserver {
            NotificationCenter.default.removeObserver(observer)
        }

        // Remove video looping observer
        if let videoPlayerItem = videoPlayerItem {
            NotificationCenter.default.removeObserver(
                NotificationCenter.default,
                name: .AVPlayerItemDidPlayToEndTime,
                object: videoPlayerItem
            )
        }
    }
    
    // MARK: - Public Getters

    var player: AVPlayer? {
        return self.videoPlayer
    }
    
    var isAudioPlaying: Bool {
        return audioState == .playing
    }
    
    var isVideoPlaying: Bool {
        return videoState == .playing
    }
}

// MARK: - AVAudioPlayerDelegate

extension MediaPlayerManager: AVAudioPlayerDelegate {
    func audioPlayerDidFinishPlaying(_ player: AVAudioPlayer, successfully flag: Bool) {
        audioState = .finished
        audioProgress = 1.0
        audioCurrentTime = audioDuration
        stopAudioProgressTracking()
    }
    
    func audioPlayerDecodeErrorDidOccur(_ player: AVAudioPlayer, error: Error?) {
        if let error = error {
            audioState = .error(MediaPlayerError.playbackFailed(error))
        }
    }
}
