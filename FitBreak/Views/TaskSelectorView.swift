//
//  TaskSelectorView.swift
//  FitBreak
//
//  Created by <PERSON> on 16/09/2025.
//

import SwiftUI
import CoreData

struct TaskSelectorView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @Environment(\.dismiss) private var dismiss
    @Binding var selectedTask: Task?
    
    @FetchRequest(
        sortDescriptors: [NSSortDescriptor(keyPath: \Task.createdAt, ascending: false)],
        animation: .default)
    private var tasks: FetchedResults<Task>
    
    @State private var showingAddTask = false
    @State private var newTaskName = ""
    @State private var newTaskDescription = ""
    
    var body: some View {
        NavigationView {
            VStack {
                if tasks.isEmpty {
                    emptyStateView
                } else {
                    taskListView
                }
            }
            .navigationTitle("Select Task")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: { showingAddTask = true }) {
                        Image(systemName: "plus")
                    }
                }
            }
            .sheet(isPresented: $showingAddTask) {
                AddTaskView()
            }
        }
    }
    
    private var emptyStateView: some View {
        VStack(spacing: 20) {
            Image(systemName: "checklist")
                .font(.system(size: 60))
                .foregroundColor(.gray)
            
            Text("No Tasks Yet")
                .font(.title2)
                .fontWeight(.semibold)
            
            Text("Create your first task to get started with focused work sessions.")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
            
            Button("Add Task") {
                showingAddTask = true
            }
            .buttonStyle(.borderedProminent)
        }
        .padding()
    }
    
    private var taskListView: some View {
        List {
            // Option to work without a specific task
            Button(action: {
                selectedTask = nil
                dismiss()
            }) {
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("No Specific Task")
                            .font(.headline)
                            .foregroundColor(.primary)
                        
                        Text("Focus session without a specific task")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    if selectedTask == nil {
                        Image(systemName: "checkmark")
                            .foregroundColor(.blue)
                    }
                }
                .contentShape(Rectangle())
            }
            .buttonStyle(.plain)
            
            Section("Your Tasks") {
                ForEach(tasks.filter { !$0.isCompleted }, id: \.self) { task in
                    TaskRowView(
                        task: task,
                        isSelected: selectedTask == task,
                        onSelect: {
                            selectedTask = task
                            dismiss()
                        }
                    )
                }
                .onDelete(perform: deleteTasks)
            }
            
            if tasks.contains(where: { $0.isCompleted }) {
                Section("Completed") {
                    ForEach(tasks.filter { $0.isCompleted }, id: \.self) { task in
                        TaskRowView(
                            task: task,
                            isSelected: false,
                            onSelect: {
                                // Don't allow selecting completed tasks
                            }
                        )
                        .opacity(0.6)
                    }
                    .onDelete(perform: deleteTasks)
                }
            }
        }
    }
    
    private func deleteTasks(offsets: IndexSet) {
        withAnimation {
            offsets.map { tasks[$0] }.forEach(viewContext.delete)
            
            do {
                try viewContext.save()
            } catch {
                // Handle the error appropriately
                print("Error deleting task: \(error)")
            }
        }
    }
}

struct TaskRowView: View {
    let task: Task
    let isSelected: Bool
    let onSelect: () -> Void
    
    @State private var showingEditTask = false
    
    var body: some View {
        Button(action: onSelect) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(task.name ?? "Unnamed Task")
                        .font(.headline)
                        .foregroundColor(.primary)
                        .strikethrough(task.isCompleted)
                    
                    if let description = task.taskDescription, !description.isEmpty {
                        Text(description)
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .lineLimit(2)
                    }
                    
                    if let createdAt = task.createdAt {
                        Text("Created \(createdAt, style: .relative) ago")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                }
                
                Spacer()
                
                VStack {
                    if isSelected {
                        Image(systemName: "checkmark")
                            .foregroundColor(.blue)
                    }
                    
                    Spacer()
                    
                    Button(action: { showingEditTask = true }) {
                        Image(systemName: "pencil")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .buttonStyle(.plain)
                }
            }
            .contentShape(Rectangle())
        }
        .buttonStyle(.plain)
        .sheet(isPresented: $showingEditTask) {
            EditTaskView(task: task)
        }
    }
}

struct AddTaskView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @Environment(\.dismiss) private var dismiss
    
    @State private var taskName = ""
    @State private var taskDescription = ""
    
    var body: some View {
        NavigationView {
            Form {
                Section("Task Details") {
                    TextField("Task name", text: $taskName)
                        .textFieldStyle(.roundedBorder)
                    
                    TextField("Description (optional)", text: $taskDescription, axis: .vertical)
                        .textFieldStyle(.roundedBorder)
                        .lineLimit(3...6)
                }
            }
            .navigationTitle("New Task")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        saveTask()
                    }
                    .disabled(taskName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
                }
            }
        }
    }
    
    private func saveTask() {
        let newTask = Task(context: viewContext)
        newTask.name = taskName.trimmingCharacters(in: .whitespacesAndNewlines)
        newTask.taskDescription = taskDescription.trimmingCharacters(in: .whitespacesAndNewlines)
        newTask.createdAt = Date()
        newTask.updatedAt = Date()
        newTask.isCompleted = false
        
        do {
            try viewContext.save()
            dismiss()
        } catch {
            print("Error saving task: \(error)")
        }
    }
}

struct EditTaskView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @Environment(\.dismiss) private var dismiss
    
    let task: Task
    
    @State private var taskName: String
    @State private var taskDescription: String
    @State private var isCompleted: Bool
    
    init(task: Task) {
        self.task = task
        self._taskName = State(initialValue: task.name ?? "")
        self._taskDescription = State(initialValue: task.taskDescription ?? "")
        self._isCompleted = State(initialValue: task.isCompleted)
    }
    
    var body: some View {
        NavigationView {
            Form {
                Section("Task Details") {
                    TextField("Task name", text: $taskName)
                        .textFieldStyle(.roundedBorder)
                    
                    TextField("Description", text: $taskDescription, axis: .vertical)
                        .textFieldStyle(.roundedBorder)
                        .lineLimit(3...6)
                }
                
                Section("Status") {
                    Toggle("Completed", isOn: $isCompleted)
                }
            }
            .navigationTitle("Edit Task")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        saveChanges()
                    }
                    .disabled(taskName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
                }
            }
        }
    }
    
    private func saveChanges() {
        task.name = taskName.trimmingCharacters(in: .whitespacesAndNewlines)
        task.taskDescription = taskDescription.trimmingCharacters(in: .whitespacesAndNewlines)
        task.isCompleted = isCompleted
        task.updatedAt = Date()
        
        do {
            try viewContext.save()
            dismiss()
        } catch {
            print("Error updating task: \(error)")
        }
    }
}

#Preview {
    TaskSelectorView(selectedTask: .constant(nil))
        .environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
}
