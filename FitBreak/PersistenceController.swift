//
//  PersistenceController.swift
//  FitBreak
//
//  Created by <PERSON> on 16/09/2025.
//

import CoreData
import Foundation

class PersistenceController {
    static let shared = PersistenceController()
    
    let container: NSPersistentContainer
    
    init(inMemory: Bool = false) {
        container = NSPersistentContainer(name: "FitBreakDataModel")
        
        if inMemory {
            container.persistentStoreDescriptions.first!.url = URL(fileURLWithPath: "/dev/null")
        }
        
        container.loadPersistentStores { _, error in
            if let error = error as NSError? {
                fatalError("Core Data error: \(error), \(error.userInfo)")
            }
        }
        
        container.viewContext.automaticallyMergesChangesFromParent = true
    }
    
    func save() {
        let context = container.viewContext
        
        if context.hasChanges {
            do {
                try context.save()
            } catch {
                let nsError = error as NSError
                fatalError("Core Data save error: \(nsError), \(nsError.userInfo)")
            }
        }
    }
    
    // MARK: - Task Management
    
    func createTask(name: String, description: String = "") -> Task {
        let context = container.viewContext
        let task = Task(context: context)
        task.name = name
        task.taskDescription = description
        task.createdAt = Date()
        task.updatedAt = Date()
        task.isCompleted = false
        
        save()
        return task
    }
    
    func updateTask(_ task: Task, name: String? = nil, description: String? = nil, isCompleted: Bool? = nil) {
        if let name = name {
            task.name = name
        }
        if let description = description {
            task.taskDescription = description
        }
        if let isCompleted = isCompleted {
            task.isCompleted = isCompleted
        }
        task.updatedAt = Date()
        save()
    }
    
    func deleteTask(_ task: Task) {
        container.viewContext.delete(task)
        save()
    }
    
    // MARK: - Session Management
    
    func createPomodoroSession(type: String, duration: Int32, task: Task? = nil, completed: Bool = false) -> PomodoroSession {
        let context = container.viewContext
        let session = PomodoroSession(context: context)
        session.sessionType = type
        session.duration = duration
        session.completedAt = Date()
        session.wasCompleted = completed
        session.task = task
        
        save()
        return session
    }
    
    // MARK: - Settings Management
    
    func getUserSettings() -> UserSettings {
        let context = container.viewContext
        let request: NSFetchRequest<UserSettings> = UserSettings.fetchRequest()
        
        do {
            let settings = try context.fetch(request)
            if let existingSettings = settings.first {
                return existingSettings
            } else {
                // Create default settings
                let newSettings = UserSettings(context: context)
                newSettings.workSessionDuration = 1500 // 25 minutes
                newSettings.shortBreakDuration = 300   // 5 minutes
                newSettings.longBreakDuration = 900    // 15 minutes
                newSettings.pomodorosUntilLongBreak = 4
                newSettings.autoStartSessions = false
                newSettings.autoStartBreaks = false
                newSettings.notificationsEnabled = true
                newSettings.selectedSoundName = "default"
                newSettings.fitnessFeatureEnabled = true
                
                save()
                return newSettings
            }
        } catch {
            fatalError("Failed to fetch user settings: \(error)")
        }
    }
    
    func updateUserSettings(_ settings: UserSettings) {
        save()
    }
}

// MARK: - Preview Support

extension PersistenceController {
    static var preview: PersistenceController = {
        let controller = PersistenceController(inMemory: true)
        let context = controller.container.viewContext
        
        // Create sample data for previews
        let sampleTask = Task(context: context)
        sampleTask.name = "Project research"
        sampleTask.taskDescription = "Research for the new project"
        sampleTask.createdAt = Date()
        sampleTask.updatedAt = Date()
        sampleTask.isCompleted = false
        
        let sampleSession = PomodoroSession(context: context)
        sampleSession.sessionType = "work"
        sampleSession.duration = 1500
        sampleSession.completedAt = Date()
        sampleSession.wasCompleted = true
        sampleSession.task = sampleTask
        
        do {
            try context.save()
        } catch {
            let nsError = error as NSError
            fatalError("Preview Core Data error: \(nsError), \(nsError.userInfo)")
        }
        
        return controller
    }()
}
