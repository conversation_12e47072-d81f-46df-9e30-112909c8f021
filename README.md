# FitBreak - Pomodoro Timer iOS App

A comprehensive Pomodoro timer app built with Swift and SwiftUI, designed to help users maintain focus and productivity through structured work sessions and breaks.

## Features

### 🍅 Core Timer Functionality
- **Customizable Work Sessions**: Default 25 minutes, adjustable from 15-60 minutes
- **Active Short Breaks**: Default 5 minutes with optional guided jumping jacks exercises
- **Long Breaks**: Default 15 minutes, adjustable from 15-45 minutes
- **Configurable Cycles**: Long break after every 4 Pomodoros (adjustable 2-8 cycles)
- **Timer Controls**: Start, Pause, Resume, Reset, and Skip
- **Visual Progress**: Circular progress indicator with real-time countdown
- **Background Operation**: Timer continues when app is backgrounded

### 🏃‍♀️ Fitness Integration
- **Guided Exercise**: Jumping jacks during short breaks with video demonstrations
- **Voice Instructions**: Audio guidance for proper exercise form
- **Motivational Messages**: Encouraging messages during workout sessions
- **Accessibility Support**: Full VoiceOver and Dynamic Type compatibility
- **Optional Feature**: Can be enabled/disabled in settings

### ⚙️ Customization Settings
- **Duration Sliders**: Easy adjustment of all timer durations
- **Auto-Start Options**: Toggle auto-start for sessions and breaks
- **Cycle Configuration**: Set Pomodoro count before long breaks
- **Persistent Settings**: User preferences saved with Core Data

### 🔔 Notifications & Audio
- **Local Push Notifications**: Alerts when sessions/breaks start and end
- **Multiple Sound Options**: Default, Bell, Chime, Ding, or Silent
- **Haptic Feedback**: Tactile feedback on modern devices
- **Permission Handling**: Proper notification consent management
- **Background Refresh**: Maintains timer accuracy when backgrounded

### ✅ Task Management
- **Task Creation**: Add tasks with names and descriptions
- **Task Association**: Link Pomodoro sessions to specific tasks
- **Completion Tracking**: Mark tasks as done
- **Session History**: View completed sessions per task
- **Search & Filter**: Find tasks quickly
- **Core Data Persistence**: Reliable data storage

### 📊 Statistics & Analytics
- **Daily Counters**: Track completed Pomodoros per day
- **Time Summaries**: Weekly and monthly focus time calculations
- **Streak Tracking**: Monitor consecutive days with completed sessions
- **Visual Charts**: Bar charts showing activity over time (iOS 16+)
- **Session History**: Recent sessions with task associations
- **Data Export**: Export statistics and settings

### 🎨 User Interface
- **Modern Design**: Clean, minimalist interface following iOS guidelines
- **Dark Mode Support**: Automatic system appearance detection
- **Accessibility**: VoiceOver support and Dynamic Type compatibility
- **Responsive Layout**: Optimized for all iPhone screen sizes
- **Smooth Animations**: Polished transitions and feedback
- **Purple Gradient Theme**: Calming, focus-oriented color scheme

## Technical Requirements

- **iOS 18.0+**: Modern API support
- **Swift 5.0**: Latest language features
- **SwiftUI**: Declarative UI framework
- **Core Data**: Local data persistence
- **UserNotifications**: Local push notifications
- **AVFoundation**: Audio playback
- **AudioToolbox**: System sounds and haptics

## Architecture

### Core Components

- **TimerManager**: Main timer logic and state management
- **SettingsManager**: User preferences and validation
- **StatisticsManager**: Data analysis and metrics
- **PersistenceController**: Core Data stack management

### Views

- **TimerView**: Main timer interface with circular progress
- **TaskSelectorView**: Task selection and management
- **TaskListView**: Complete task management interface
- **StatisticsView**: Analytics and progress visualization
- **SettingsView**: Comprehensive settings configuration

### Data Models

- **Task**: User tasks with descriptions and completion status
- **PomodoroSession**: Individual timer sessions with metadata
- **UserSettings**: App configuration and preferences

## Installation

1. Clone the repository
2. Open `FitBreak.xcodeproj` in Xcode
3. Select your target device or simulator
4. Build and run the project

## Usage

### Getting Started

1. **First Launch**: Grant notification permissions for session alerts
2. **Create Tasks**: Add tasks you want to focus on (optional)
3. **Configure Settings**: Adjust timer durations to your preference
4. **Start Focusing**: Select a task and start your first Pomodoro session

### Timer Operation

1. **Select Session Type**: Choose Work, Short Break, or Long Break
2. **Start Timer**: Tap the play button to begin countdown
3. **Stay Focused**: Work until the timer completes
4. **Take Breaks**: Follow the natural Pomodoro rhythm
5. **Track Progress**: Monitor your daily and weekly statistics

### Customization

- **Timer Durations**: Adjust in Settings using sliders
- **Auto-Start**: Enable automatic session transitions
- **Sounds**: Choose from multiple completion sound options
- **Notifications**: Configure alert preferences

## Testing

The app includes comprehensive unit tests covering:

- Core Data operations (CRUD for tasks and sessions)
- Settings validation and persistence
- Timer state management and transitions
- Statistics calculations and performance
- Data export/import functionality

Run tests using `Cmd+U` in Xcode or through the Test Navigator.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## Privacy

FitBreak respects user privacy:

- All data is stored locally on the device
- No analytics or tracking
- No network requests or data transmission
- Notification permissions requested with clear explanation

## License

This project is available under the MIT License. See LICENSE file for details.

## Support

For issues, feature requests, or questions:

1. Check existing GitHub issues
2. Create a new issue with detailed description
3. Include device model and iOS version for bugs

## Roadmap

Future enhancements may include:

- Apple Watch companion app
- Widget support for quick timer access
- iCloud sync across devices
- Advanced statistics and insights
- Focus mode integration
- Productivity tips and guidance

---

**FitBreak** - Focus • Break • Achieve
