//
//  TaskListView.swift
//  FitBreak
//
//  Created by <PERSON> on 16/09/2025.
//

import SwiftUI
import CoreData

struct TaskListView: View {
    @Environment(\.managedObjectContext) private var viewContext
    
    @FetchRequest(
        sortDescriptors: [
            NSSortDescriptor(keyPath: \Task.isCompleted, ascending: true),
            NSSortDescriptor(keyPath: \Task.updatedAt, ascending: false)
        ],
        animation: .default)
    private var tasks: FetchedResults<Task>
    
    @State private var showingAddTask = false
    @State private var searchText = ""
    
    var filteredTasks: [Task] {
        if searchText.isEmpty {
            return Array(tasks)
        } else {
            return tasks.filter { task in
                (task.name?.localizedCaseInsensitiveContains(searchText) ?? false) ||
                (task.taskDescription?.localizedCaseInsensitiveContains(searchText) ?? false)
            }
        }
    }
    
    var activeTasks: [Task] {
        filteredTasks.filter { !$0.isCompleted }
    }
    
    var completedTasks: [Task] {
        filteredTasks.filter { $0.isCompleted }
    }
    
    var body: some View {
        NavigationView {
            VStack {
                if tasks.isEmpty && searchText.isEmpty {
                    emptyStateView
                } else {
                    taskListContent
                }
            }
            .navigationTitle("Tasks")
            .searchable(text: $searchText, prompt: "Search tasks...")
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: { showingAddTask = true }) {
                        Image(systemName: "plus")
                    }
                }
            }
            .sheet(isPresented: $showingAddTask) {
                AddTaskView()
            }
        }
    }
    
    private var emptyStateView: some View {
        VStack(spacing: 20) {
            Image(systemName: "checklist")
                .font(.system(size: 60))
                .foregroundColor(.gray)
            
            Text("No Tasks Yet")
                .font(.title2)
                .fontWeight(.semibold)
            
            Text("Create tasks to organize your Pomodoro sessions and track your progress.")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
            
            Button("Add Your First Task") {
                showingAddTask = true
            }
            .buttonStyle(.borderedProminent)
        }
        .padding()
    }
    
    private var taskListContent: some View {
        List {
            if !activeTasks.isEmpty {
                Section("Active Tasks") {
                    ForEach(activeTasks, id: \.self) { task in
                        TaskListRowView(task: task)
                    }
                    .onDelete { indexSet in
                        deleteActiveTasks(offsets: indexSet)
                    }
                }
            }
            
            if !completedTasks.isEmpty {
                Section("Completed") {
                    ForEach(completedTasks, id: \.self) { task in
                        TaskListRowView(task: task)
                            .opacity(0.7)
                    }
                    .onDelete { indexSet in
                        deleteCompletedTasks(offsets: indexSet)
                    }
                }
            }
            
            if filteredTasks.isEmpty && !searchText.isEmpty {
                Section {
                    Text("No tasks match your search")
                        .foregroundColor(.secondary)
                        .italic()
                }
            }
        }
    }
    
    private func deleteActiveTasks(offsets: IndexSet) {
        withAnimation {
            offsets.map { activeTasks[$0] }.forEach(viewContext.delete)
            saveContext()
        }
    }
    
    private func deleteCompletedTasks(offsets: IndexSet) {
        withAnimation {
            offsets.map { completedTasks[$0] }.forEach(viewContext.delete)
            saveContext()
        }
    }
    
    private func saveContext() {
        do {
            try viewContext.save()
        } catch {
            print("Error saving context: \(error)")
        }
    }
}

struct TaskListRowView: View {
    @ObservedObject var task: Task
    @Environment(\.managedObjectContext) private var viewContext
    @State private var showingEditTask = false
    
    var body: some View {
        HStack {
            Button(action: toggleCompletion) {
                Image(systemName: task.isCompleted ? "checkmark.circle.fill" : "circle")
                    .foregroundColor(task.isCompleted ? .green : .gray)
                    .font(.title2)
            }
            .buttonStyle(.plain)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(task.name ?? "Unnamed Task")
                    .font(.headline)
                    .strikethrough(task.isCompleted)
                    .foregroundColor(task.isCompleted ? .secondary : .primary)
                
                if let description = task.taskDescription, !description.isEmpty {
                    Text(description)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .lineLimit(2)
                }
                
                HStack {
                    if let createdAt = task.createdAt {
                        Text("Created \(createdAt, style: .relative) ago")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    if let sessions = task.sessions?.allObjects as? [PomodoroSession] {
                        let completedSessions = sessions.filter { $0.wasCompleted && $0.sessionType == "work" }
                        if !completedSessions.isEmpty {
                            HStack(spacing: 4) {
                                Image(systemName: "timer")
                                    .font(.caption2)
                                Text("\(completedSessions.count)")
                                    .font(.caption2)
                            }
                            .foregroundColor(.secondary)
                        }
                    }
                }
            }
            
            Spacer()
            
            Button(action: { showingEditTask = true }) {
                Image(systemName: "pencil")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .buttonStyle(.plain)
        }
        .contentShape(Rectangle())
        .sheet(isPresented: $showingEditTask) {
            EditTaskView(task: task)
        }
    }
    
    private func toggleCompletion() {
        withAnimation {
            task.isCompleted.toggle()
            task.updatedAt = Date()
            
            do {
                try viewContext.save()
            } catch {
                print("Error toggling task completion: \(error)")
            }
        }
    }
}

#Preview {
    TaskListView()
        .environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
}
